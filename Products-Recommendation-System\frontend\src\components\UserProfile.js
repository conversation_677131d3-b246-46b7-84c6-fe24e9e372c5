import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { toast } from 'sonner';
import { 
  User, 
  Mail, 
  Calendar, 
  Activity, 
  Eye,
  ShoppingCart,
  Star,
  Package,
  TrendingUp,
  Clock
} from 'lucide-react';

const UserProfile = ({ user }) => {
  const [userHistory, setUserHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalInteractions: 0,
    categoriesInteracted: 0,
    mostViewedCategory: '',
    recentActivity: 0
  });

  useEffect(() => {
    loadUserHistory();
  }, []);

  const loadUserHistory = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/user/history');
      setUserHistory(response.data);
      calculateStats(response.data);
    } catch (error) {
      toast.error('Failed to load user history');
      console.error('History error:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (history) => {
    const interactions = history.map(item => item.interaction);
    const totalInteractions = interactions.length;
    
    const categories = new Set(history.map(item => item.product?.category).filter(Boolean));
    const categoriesInteracted = categories.size;
    
    const categoryCount = {};
    history.forEach(item => {
      if (item.product?.category) {
        categoryCount[item.product.category] = (categoryCount[item.product.category] || 0) + 1;
      }
    });
    
    const mostViewedCategory = Object.keys(categoryCount).reduce((a, b) => 
      categoryCount[a] > categoryCount[b] ? a : b, ''
    );
    
    const now = new Date();
    const recentActivity = interactions.filter(interaction => {
      const interactionDate = new Date(interaction.timestamp);
      const daysDiff = (now - interactionDate) / (1000 * 60 * 60 * 24);
      return daysDiff <= 7;
    }).length;
    
    setStats({
      totalInteractions,
      categoriesInteracted,
      mostViewedCategory,
      recentActivity
    });
  };

  const getEventIcon = (eventType) => {
    switch (eventType) {
      case 'view':
        return <Eye className="w-4 h-4 text-blue-600" />;
      case 'click':
        return <Activity className="w-4 h-4 text-green-600" />;
      case 'add_to_cart':
        return <ShoppingCart className="w-4 h-4 text-orange-600" />;
      case 'purchase':
        return <Package className="w-4 h-4 text-purple-600" />;
      case 'rating':
        return <Star className="w-4 h-4 text-yellow-600" />;
      default:
        return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  const getEventColor = (eventType) => {
    switch (eventType) {
      case 'view':
        return 'bg-blue-100 text-blue-800';
      case 'click':
        return 'bg-green-100 text-green-800';
      case 'add_to_cart':
        return 'bg-orange-100 text-orange-800';
      case 'purchase':
        return 'bg-purple-100 text-purple-800';
      case 'rating':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} hours ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} days ago`;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="loading-dots">
              <div></div>
              <div></div>
              <div></div>
              <div></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="fade-in">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            User <span className="text-gradient">Profile</span>
          </h1>
          <p className="text-gray-600">
            View your account information and activity history
          </p>
        </div>

        {/* Profile Info */}
        <div className="card slide-up">
          <div className="flex items-center gap-6 mb-6">
            <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white text-2xl font-bold">
                {user.name.charAt(0).toUpperCase()}
              </span>
            </div>
            
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">{user.name}</h2>
              <div className="space-y-1">
                <div className="flex items-center gap-2 text-gray-600">
                  <Mail className="w-4 h-4" />
                  <span>{user.email}</span>
                </div>
                <div className="flex items-center gap-2 text-gray-600">
                  <Calendar className="w-4 h-4" />
                  <span>Member since {new Date(user.signup_date).toLocaleDateString()}</span>
                </div>
                {user.age_group && (
                  <div className="flex items-center gap-2 text-gray-600">
                    <User className="w-4 h-4" />
                    <span>Age group: {user.age_group}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="stat-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Interactions</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalInteractions}</p>
                </div>
                <Activity className="w-8 h-8 text-blue-600" />
              </div>
            </div>
            
            <div className="stat-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Categories</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.categoriesInteracted}</p>
                </div>
                <Package className="w-8 h-8 text-green-600" />
              </div>
            </div>
            
            <div className="stat-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Most Viewed</p>
                  <p className="text-lg font-bold text-gray-900 truncate">{stats.mostViewedCategory || 'N/A'}</p>
                </div>
                <TrendingUp className="w-8 h-8 text-purple-600" />
              </div>
            </div>
            
            <div className="stat-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Recent Activity</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.recentActivity}</p>
                </div>
                <Clock className="w-8 h-8 text-orange-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Activity History */}
        <div className="card">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
              <Activity className="w-5 h-5" />
              Activity History
            </h3>
            <Button
              onClick={loadUserHistory}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Activity className="w-4 h-4" />
              Refresh
            </Button>
          </div>
          
          {userHistory.length > 0 ? (
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {userHistory
                .sort((a, b) => new Date(b.interaction.timestamp) - new Date(a.interaction.timestamp))
                .map((item, index) => (
                  <div 
                    key={`${item.interaction.user_id}-${item.interaction.product_id}-${index}`}
                    className="card-compact flex items-center gap-4 hover:bg-blue-50 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      {getEventIcon(item.interaction.event_type)}
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg flex items-center justify-center">
                        <Package className="w-6 h-6 text-blue-600" />
                      </div>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-gray-900 truncate">
                        {item.product?.title || 'Product not found'}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {item.product?.category} • {item.product?.brand}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatDate(item.interaction.timestamp)}
                      </p>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getEventColor(item.interaction.event_type)}`}>
                        {item.interaction.event_type}
                      </span>
                      {item.interaction.event_type === 'rating' && (
                        <div className="flex items-center gap-1 text-yellow-600">
                          <Star className="w-4 h-4 fill-current" />
                          <span className="text-sm">{item.interaction.value}</span>
                        </div>
                      )}
                      {item.product?.price && (
                        <span className="text-sm font-semibold text-green-600">
                          ₹{item.product.price}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
            </div>
          ) : (
            <div className="text-center py-12 text-gray-500">
              <Activity className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p>No activity history found</p>
              <p className="text-sm">Start interacting with products to see your activity here</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserProfile;