import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { toast } from 'sonner';
import { 
  TrendingUp, 
  Users, 
  ShoppingCart, 
  BarChart3,
  Star,
  Eye,
  RefreshCw,
  Sparkles,
  Package,
  Activity
} from 'lucide-react';
import Chart from './Chart';

const Dashboard = ({ user }) => {
  const [recommendations, setRecommendations] = useState([]);
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState('item_based');

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [recsResponse, analyticsResponse] = await Promise.all([
        axios.get(`/recommendations?method=${selectedMethod}&n=10`),
        axios.get('/analytics')
      ]);
      
      setRecommendations(recsResponse.data);
      setAnalytics(analyticsResponse.data);
    } catch (error) {
      toast.error('Failed to load dashboard data');
      console.error('Dashboard error:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshRecommendations = async () => {
    try {
      setRefreshing(true);
      const response = await axios.get(`/recommendations?method=${selectedMethod}&n=10`);
      setRecommendations(response.data);
      toast.success('Recommendations refreshed!');
    } catch (error) {
      toast.error('Failed to refresh recommendations');
    } finally {
      setRefreshing(false);
    }
  };

  const handleProductInteraction = async (productId, eventType) => {
    try {
      await axios.post('/interactions', {
        product_id: productId,
        event_type: eventType,
        value: eventType === 'view' ? 1 : eventType === 'click' ? 2 : 4
      });
      
      toast.success(`${eventType} recorded!`);
      
      // Refresh recommendations after interaction
      if (eventType !== 'view') {
        await refreshRecommendations();
      }
    } catch (error) {
      toast.error('Failed to record interaction');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="loading-dots">
              <div></div>
              <div></div>
              <div></div>
              <div></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 fade-in">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Welcome back, <span className="text-gradient">{user.name}</span>
            </h1>
            <p className="text-gray-600">
              Here are your personalized recommendations and insights
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            <select
              value={selectedMethod}
              onChange={(e) => setSelectedMethod(e.target.value)}
              className="custom-select"
            >
              <option value="item_based">Item-Based CF</option>
              <option value="svd">Matrix Factorization</option>
            </select>
            
            <Button 
              onClick={refreshRecommendations}
              disabled={refreshing}
              className="flex items-center gap-2 btn-secondary"
            >
              <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Analytics Overview */}
        {analytics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 slide-up">
            <div className="stat-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <p className="text-2xl font-bold text-gray-900">{analytics.total_users.toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <Users className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </div>
            
            <div className="stat-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Products</p>
                  <p className="text-2xl font-bold text-gray-900">{analytics.total_products.toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <Package className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </div>
            
            <div className="stat-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Interactions</p>
                  <p className="text-2xl font-bold text-gray-900">{analytics.total_interactions.toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <Activity className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </div>
            
            <div className="stat-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Categories</p>
                  <p className="text-2xl font-bold text-gray-900">{Object.keys(analytics.category_distribution).length}</p>
                </div>
                <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recommendations */}
          <div className="lg:col-span-2 space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                <Sparkles className="w-6 h-6 text-yellow-500" />
                Your Recommendations
              </h2>
              <div className="text-sm text-gray-500">
                {selectedMethod === 'item_based' ? 'Item-Based Filtering' : 'Matrix Factorization'}
              </div>
            </div>
            
            <div className="space-y-4">
              {recommendations.length > 0 ? (
                recommendations.map((product, index) => (
                  <div 
                    key={product.product_id} 
                    className="card-compact flex items-center gap-4 bounce-in"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg flex items-center justify-center">
                      <Package className="w-8 h-8 text-blue-600" />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-gray-900 truncate">{product.title}</h3>
                      <p className="text-sm text-gray-600">{product.category} • {product.brand}</p>
                      <div className="flex items-center gap-4 mt-2">
                        <span className="text-lg font-bold text-green-600">₹{product.price}</span>
                        <div className="flex items-center gap-1 text-sm text-yellow-600">
                          <Star className="w-4 h-4 fill-current" />
                          <span>{product.score.toFixed(2)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex flex-col gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleProductInteraction(product.product_id, 'view')}
                        className="flex items-center gap-1"
                      >
                        <Eye className="w-4 h-4" />
                        View
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleProductInteraction(product.product_id, 'add_to_cart')}
                        className="flex items-center gap-1 btn-primary"
                      >
                        <ShoppingCart className="w-4 h-4" />
                        Add
                      </Button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <Package className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p>No recommendations available</p>
                </div>
              )}
            </div>
          </div>

          {/* Charts */}
          <div className="space-y-6">
            {analytics && (
              <>
                {/* Category Distribution */}
                <div className="chart-container">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <BarChart3 className="w-5 h-5" />
                    Category Distribution
                  </h3>
                  <Chart 
                    type="pie"
                    data={analytics.category_distribution}
                    title="Products by Category"
                  />
                </div>

                {/* Popular Products */}
                <div className="chart-container">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Popular Products
                  </h3>
                  <div className="space-y-3">
                    {analytics.popular_products.slice(0, 5).map((product, index) => (
                      <div key={product.product_id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-bold text-blue-600">
                            {index + 1}
                          </div>
                          <span className="font-medium text-gray-900 text-sm">{product.title}</span>
                        </div>
                        <span className="text-sm text-gray-600">{product.interactions}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Activity Timeline */}
                <div className="chart-container">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <Activity className="w-5 h-5" />
                    User Activity
                  </h3>
                  <Chart 
                    type="line"
                    data={analytics.daily_activity}
                    title="Daily Interactions"
                  />
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;