# Repository Overview

- **Root**: c:\Users\<USER>\Products-recommendation-system
- **Primary app folder**: Products-Recommendation-System

## Structure
- **Products-Recommendation-System/backend**: FastAPI service (MongoDB via motor)
  - server.py: API, CORS, startup init (synthetic data + model training)
  - recommendation_engine.py: CF + SVD models
  - data_generator.py: Synthetic data generation
  - requirements.txt: Python deps
  - .env (local): MONGO_URL, DB_NAME, JWT_SECRET, etc.
- **Products-Recommendation-System/frontend**: React (CRA + craco + Tailwind)
  - package.json: build with `craco build`
  - netlify.toml: SPA routing + build
  - Expects `REACT_APP_BACKEND_URL` for API base
- **.github/workflows/deploy-frontend-netlify.yml**: Optional CI deploy to Netlify

## Deploy Targets
- **Frontend**: Netlify (UI or GitHub Actions). Publish dir: `build`, command: `yarn build`, base dir: `Products-Recommendation-System/frontend`.
- **Backend**: Render (Web Service). Root dir: `Products-Recommendation-System/backend`.
  - Build: `pip install -r requirements.txt`
  - Start: `uvicorn server:app --host 0.0.0.0 --port $PORT`

## Required Environment Variables
- **Backend** (Render):
  - MONGO_URL: <MongoDB Atlas connection string>
  - DB_NAME: recommendation (or chosen)
  - JWT_SECRET: long random string
  - CORS_ORIGINS: https://<your-netlify-site>.netlify.app
- **Frontend** (Netlify):
  - REACT_APP_BACKEND_URL: https://<your-backend>.onrender.com

## Notes
- On the first backend startup, synthetic data is generated and models are trained; may take time.
- CORS must include the Netlify origin. SPA redirects are configured in netlify.toml.