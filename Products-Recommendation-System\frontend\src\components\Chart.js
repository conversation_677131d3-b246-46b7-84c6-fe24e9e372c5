import React from 'react';

const Chart = ({ type, data, title }) => {
  if (!data || Object.keys(data).length === 0) {
    return (
      <div className="flex items-center justify-center h-48 text-gray-400">
        <p>No data available</p>
      </div>
    );
  }

  const renderPieChart = () => {
    const total = Object.values(data).reduce((sum, value) => sum + value, 0);
    const colors = [
      '#3b82f6', '#8b5cf6', '#06d6a0', '#f59e0b', '#ef4444',
      '#6366f1', '#ec4899', '#10b981', '#f97316', '#84cc16'
    ];
    
    let currentAngle = 0;
    const center = 80;
    const radius = 70;

    return (
      <div className="flex items-center justify-center">
        <div className="relative">
          <svg width="160" height="160" className="transform -rotate-90">
            {Object.entries(data).map(([key, value], index) => {
              const percentage = (value / total) * 100;
              const angle = (value / total) * 360;
              const x1 = center + radius * Math.cos((currentAngle * Math.PI) / 180);
              const y1 = center + radius * Math.sin((currentAngle * Math.PI) / 180);
              const x2 = center + radius * Math.cos(((currentAngle + angle) * Math.PI) / 180);
              const y2 = center + radius * Math.sin(((currentAngle + angle) * Math.PI) / 180);
              
              const largeArcFlag = angle > 180 ? 1 : 0;
              const pathData = [
                `M ${center} ${center}`,
                `L ${x1} ${y1}`,
                `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                'Z'
              ].join(' ');
              
              const result = (
                <path
                  key={key}
                  d={pathData}
                  fill={colors[index % colors.length]}
                  className="hover:opacity-80 transition-opacity"
                />
              );
              
              currentAngle += angle;
              return result;
            })}
          </svg>
          
          {/* Legend */}
          <div className="absolute -right-32 top-0 space-y-2">
            {Object.entries(data).map(([key, value], index) => {
              const percentage = ((value / total) * 100).toFixed(1);
              return (
                <div key={key} className="flex items-center gap-2 text-xs">
                  <div 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: colors[index % colors.length] }}
                  />
                  <span className="text-gray-700">{key}</span>
                  <span className="text-gray-500">({percentage}%)</span>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  const renderBarChart = () => {
    const maxValue = Math.max(...Object.values(data));
    const colors = ['#3b82f6', '#8b5cf6', '#06d6a0', '#f59e0b', '#ef4444'];
    
    return (
      <div className="space-y-3">
        {Object.entries(data).slice(0, 8).map(([key, value], index) => {
          const percentage = (value / maxValue) * 100;
          return (
            <div key={key} className="space-y-1">
              <div className="flex justify-between text-sm">
                <span className="text-gray-700 truncate">{key}</span>
                <span className="text-gray-600">{value}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="h-2 rounded-full transition-all duration-500 ease-out"
                  style={{
                    width: `${percentage}%`,
                    backgroundColor: colors[index % colors.length]
                  }}
                />
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderLineChart = () => {
    const entries = Object.entries(data).sort((a, b) => new Date(a[0]) - new Date(b[0]));
    const maxValue = Math.max(...entries.map(([, value]) => value));
    const minValue = Math.min(...entries.map(([, value]) => value));
    const range = maxValue - minValue || 1;
    
    const width = 300;
    const height = 120;
    const padding = 20;
    
    const points = entries.map(([date, value], index) => {
      const x = padding + (index / (entries.length - 1)) * (width - 2 * padding);
      const y = height - padding - ((value - minValue) / range) * (height - 2 * padding);
      return { x, y, value, date };
    });
    
    const pathData = points
      .map((point, index) => `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`)
      .join(' ');
    
    return (
      <div className="flex justify-center">
        <svg width={width} height={height} className="overflow-visible">
          {/* Grid lines */}
          {[0, 0.25, 0.5, 0.75, 1].map((ratio) => (
            <line
              key={ratio}
              x1={padding}
              y1={padding + ratio * (height - 2 * padding)}
              x2={width - padding}
              y2={padding + ratio * (height - 2 * padding)}
              stroke="#e5e7eb"
              strokeWidth="1"
            />
          ))}
          
          {/* Line */}
          <path
            d={pathData}
            fill="none"
            stroke="#3b82f6"
            strokeWidth="2"
            className="drop-shadow-sm"
          />
          
          {/* Points */}
          {points.map((point, index) => (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r="3"
              fill="#3b82f6"
              className="hover:r-4 transition-all"
            />
          ))}
          
          {/* Labels */}
          <text x={padding} y={height - 5} className="text-xs fill-gray-500">
            {entries[0]?.[0]}
          </text>
          <text x={width - padding} y={height - 5} className="text-xs fill-gray-500 text-end">
            {entries[entries.length - 1]?.[0]}
          </text>
        </svg>
      </div>
    );
  };

  return (
    <div className="w-full">
      {type === 'pie' && renderPieChart()}
      {type === 'bar' && renderBarChart()}
      {type === 'line' && renderLineChart()}
    </div>
  );
};

export default Chart;