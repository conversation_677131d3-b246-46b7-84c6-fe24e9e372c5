{"version": "1.0", "examples": {"AddTagsToResource": [{"input": {"ResourceName": "arn:aws:elasticache:us-east-1:1234567890:cluster:my-mem-cluster", "Tags": [{"Key": "APIVersion", "Value": "********"}, {"Key": "Service", "Value": "ElastiCache"}]}, "output": {"TagList": [{"Key": "APIVersion", "Value": "********"}, {"Key": "Service", "Value": "ElastiCache"}]}, "comments": {"input": {}, "output": {}}, "description": "Adds up to 10 tags, key/value pairs, to a cluster or snapshot resource.", "id": "addtagstoresource-1482430264385", "title": "AddTagsToResource"}], "AuthorizeCacheSecurityGroupIngress": [{"input": {"CacheSecurityGroupName": "my-sec-grp", "EC2SecurityGroupName": "my-ec2-sec-grp", "EC2SecurityGroupOwnerId": "1234567890"}, "comments": {"input": {}, "output": {}}, "description": "Allows network ingress to a cache security group. Applications using ElastiCache must be running on Amazon EC2. Amazon EC2 security groups are used as the authorization mechanism.", "id": "authorizecachecachesecuritygroupingress-1483046446206", "title": "AuthorizeCacheCacheSecurityGroupIngress"}], "CopySnapshot": [{"input": {"SourceSnapshotName": "my-snapshot", "TargetBucket": "", "TargetSnapshotName": "my-snapshot-copy"}, "output": {"Snapshot": {"AutoMinorVersionUpgrade": true, "CacheClusterCreateTime": "2016-12-21T22:24:04.955Z", "CacheClusterId": "my-redis4", "CacheNodeType": "cache.m3.large", "CacheParameterGroupName": "default.redis3.2", "CacheSubnetGroupName": "default", "Engine": "redis", "EngineVersion": "3.2.4", "NodeSnapshots": [{"CacheNodeCreateTime": "2016-12-21T22:24:04.955Z", "CacheNodeId": "0001", "CacheSize": "3 MB", "SnapshotCreateTime": "2016-12-28T07:00:52Z"}], "NumCacheNodes": 1, "Port": 6379, "PreferredAvailabilityZone": "us-east-1c", "PreferredMaintenanceWindow": "tue:09:30-tue:10:30", "SnapshotName": "my-snapshot-copy", "SnapshotRetentionLimit": 7, "SnapshotSource": "manual", "SnapshotStatus": "creating", "SnapshotWindow": "07:00-08:00", "VpcId": "vpc-3820329f3"}}, "comments": {"input": {}, "output": {}}, "description": "Copies a snapshot to a specified name.", "id": "copysnapshot-1482961393820", "title": "CopySnapshot"}], "CreateCacheCluster": [{"input": {"AZMode": "cross-az", "CacheClusterId": "my-memcached-cluster", "CacheNodeType": "cache.r3.large", "CacheSubnetGroupName": "default", "Engine": "memcached", "EngineVersion": "1.4.24", "NumCacheNodes": 2, "Port": 11211}, "output": {"CacheCluster": {"AutoMinorVersionUpgrade": true, "CacheClusterId": "my-memcached-cluster", "CacheClusterStatus": "creating", "CacheNodeType": "cache.r3.large", "CacheParameterGroup": {"CacheNodeIdsToReboot": [], "CacheParameterGroupName": "default.memcached1.4", "ParameterApplyStatus": "in-sync"}, "CacheSecurityGroups": [], "CacheSubnetGroupName": "default", "ClientDownloadLandingPage": "https://console.aws.amazon.com/elasticache/home#client-download:", "Engine": "memcached", "EngineVersion": "1.4.24", "NumCacheNodes": 2, "PendingModifiedValues": {}, "PreferredAvailabilityZone": "Multiple", "PreferredMaintenanceWindow": "wed:09:00-wed:10:00"}}, "comments": {"input": {}, "output": {}}, "description": "Creates a Memcached cluster with 2 nodes. ", "id": "createcachecluster-1474994727381", "title": "CreateCacheCluster"}, {"input": {"AutoMinorVersionUpgrade": true, "CacheClusterId": "my-redis", "CacheNodeType": "cache.r3.larage", "CacheSubnetGroupName": "default", "Engine": "redis", "EngineVersion": "3.2.4", "NumCacheNodes": 1, "Port": 6379, "PreferredAvailabilityZone": "us-east-1c", "SnapshotRetentionLimit": 7}, "output": {"CacheCluster": {"AutoMinorVersionUpgrade": true, "CacheClusterId": "my-redis", "CacheClusterStatus": "creating", "CacheNodeType": "cache.m3.large", "CacheParameterGroup": {"CacheNodeIdsToReboot": [], "CacheParameterGroupName": "default.redis3.2", "ParameterApplyStatus": "in-sync"}, "CacheSecurityGroups": [], "CacheSubnetGroupName": "default", "ClientDownloadLandingPage": "https: //console.aws.amazon.com/elasticache/home#client-download: ", "Engine": "redis", "EngineVersion": "3.2.4", "NumCacheNodes": 1, "PendingModifiedValues": {}, "PreferredAvailabilityZone": "us-east-1c", "PreferredMaintenanceWindow": "fri: 05: 30-fri: 06: 30", "SnapshotRetentionLimit": 7, "SnapshotWindow": "10: 00-11: 00"}}, "comments": {"input": {}, "output": {}}, "description": "Creates a Redis cluster with 1 node. ", "id": "createcachecluster-1474994727381", "title": "CreateCacheCluster"}], "CreateCacheParameterGroup": [{"input": {"CacheParameterGroupFamily": "redis2.8", "CacheParameterGroupName": "custom-redis2-8", "Description": "Custom Redis 2.8 parameter group."}, "output": {"CacheParameterGroup": {"CacheParameterGroupFamily": "redis2.8", "CacheParameterGroupName": "custom-redis2-8", "Description": "Custom Redis 2.8 parameter group."}}, "comments": {"input": {}, "output": {}}, "description": "Creates the Amazon ElastiCache parameter group custom-redis2-8.", "id": "createcacheparametergroup-1474997699362", "title": "CreateCacheParameterGroup"}], "CreateCacheSecurityGroup": [{"input": {"CacheSecurityGroupName": "my-cache-sec-grp", "Description": "Example ElastiCache security group."}, "comments": {"input": {}, "output": {}}, "description": "Creates an ElastiCache security group. ElastiCache security groups are only for clusters not running in an AWS VPC.", "id": "createcachesecuritygroup-1483041506604", "title": "CreateCacheSecurityGroup"}], "CreateCacheSubnetGroup": [{"input": {"CacheSubnetGroupDescription": "Sample subnet group", "CacheSubnetGroupName": "my-sn-grp2", "SubnetIds": ["subnet-6f28c982", "subnet-bcd382f3", "subnet-845b3e7c0"]}, "output": {"CacheSubnetGroup": {"CacheSubnetGroupDescription": "My subnet group.", "CacheSubnetGroupName": "my-sn-grp", "Subnets": [{"SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetIdentifier": "subnet-6f28c982"}, {"SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetIdentifier": "subnet-bcd382f3"}, {"SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetIdentifier": "subnet-845b3e7c0"}], "VpcId": "vpc-91280df6"}}, "comments": {"input": {}, "output": {}}, "description": "Creates a new cache subnet group.", "id": "createcachesubnet-1483042274558", "title": "CreateCacheSubnet"}], "CreateReplicationGroup": [{"input": {"AutomaticFailoverEnabled": true, "CacheNodeType": "cache.m3.medium", "Engine": "redis", "EngineVersion": "2.8.24", "NumCacheClusters": 3, "ReplicationGroupDescription": "A Redis replication group.", "ReplicationGroupId": "my-redis-rg", "SnapshotRetentionLimit": 30}, "output": {"ReplicationGroup": {"AutomaticFailover": "enabling", "Description": "A Redis replication group.", "MemberClusters": ["my-redis-rg-001", "my-redis-rg-002", "my-redis-rg-003"], "PendingModifiedValues": {}, "ReplicationGroupId": "my-redis-rg", "SnapshottingClusterId": "my-redis-rg-002", "Status": "creating"}}, "comments": {"input": {}, "output": {}}, "description": "Creates a Redis replication group with 3 nodes.", "id": "createcachereplicationgroup-1474998730655", "title": "CreateCacheReplicationGroup"}, {"input": {"AutoMinorVersionUpgrade": true, "CacheNodeType": "cache.m3.medium", "CacheParameterGroupName": "default.redis3.2.cluster.on", "Engine": "redis", "EngineVersion": "3.2.4", "NodeGroupConfiguration": [{"PrimaryAvailabilityZone": "us-east-1c", "ReplicaAvailabilityZones": ["us-east-1b"], "ReplicaCount": 1, "Slots": "0-8999"}, {"PrimaryAvailabilityZone": "us-east-1a", "ReplicaAvailabilityZones": ["us-east-1a", "us-east-1c"], "ReplicaCount": 2, "Slots": "9000-16383"}], "NumNodeGroups": 2, "ReplicationGroupDescription": "A multi-sharded replication group", "ReplicationGroupId": "clustered-redis-rg", "SnapshotRetentionLimit": 8}, "output": {"ReplicationGroup": {"AutomaticFailover": "enabled", "Description": "Sharded replication group", "MemberClusters": ["rc-rg3-0001-001", "rc-rg3-0001-002", "rc-rg3-0002-001", "rc-rg3-0002-002", "rc-rg3-0002-003"], "PendingModifiedValues": {}, "ReplicationGroupId": "clustered-redis-rg", "SnapshotRetentionLimit": 8, "SnapshotWindow": "05:30-06:30", "Status": "creating"}}, "comments": {"input": {}, "output": {}}, "description": "Creates a Redis (cluster mode enabled) replication group with two shards. One shard has one read replica node and the other shard has two read replicas.", "id": "createreplicationgroup-1483657035585", "title": "CreateReplicationGroup"}], "CreateSnapshot": [{"input": {"CacheClusterId": "onenoderedis", "SnapshotName": "snapshot-1"}, "output": {"Snapshot": {"AutoMinorVersionUpgrade": true, "CacheClusterCreateTime": "2017-02-03T15:43:36.278Z", "CacheClusterId": "onenoderedis", "CacheNodeType": "cache.m3.medium", "CacheParameterGroupName": "default.redis3.2", "CacheSubnetGroupName": "default", "Engine": "redis", "EngineVersion": "3.2.4", "NodeSnapshots": [{"CacheNodeCreateTime": "2017-02-03T15:43:36.278Z", "CacheNodeId": "0001", "CacheSize": ""}], "NumCacheNodes": 1, "Port": 6379, "PreferredAvailabilityZone": "us-west-2c", "PreferredMaintenanceWindow": "sat:08:00-sat:09:00", "SnapshotName": "snapshot-1", "SnapshotRetentionLimit": 1, "SnapshotSource": "manual", "SnapshotStatus": "creating", "SnapshotWindow": "00:00-01:00", "VpcId": "vpc-73c3cd17"}}, "comments": {"input": {}, "output": {}}, "description": "Creates a snapshot of a non-clustered Redis cluster that has only one node.", "id": "createsnapshot-1474999681024", "title": "CreateSnapshot - NonClustered Redis, no read-replicas"}, {"input": {"CacheClusterId": "threenoderedis-001", "SnapshotName": "snapshot-2"}, "output": {"Snapshot": {"AutoMinorVersionUpgrade": true, "CacheClusterCreateTime": "2017-02-03T15:43:36.278Z", "CacheClusterId": "threenoderedis-001", "CacheNodeType": "cache.m3.medium", "CacheParameterGroupName": "default.redis3.2", "CacheSubnetGroupName": "default", "Engine": "redis", "EngineVersion": "3.2.4", "NodeSnapshots": [{"CacheNodeCreateTime": "2017-02-03T15:43:36.278Z", "CacheNodeId": "0001", "CacheSize": ""}], "NumCacheNodes": 1, "Port": 6379, "PreferredAvailabilityZone": "us-west-2c", "PreferredMaintenanceWindow": "sat:08:00-sat:09:00", "SnapshotName": "snapshot-2", "SnapshotRetentionLimit": 1, "SnapshotSource": "manual", "SnapshotStatus": "creating", "SnapshotWindow": "00:00-01:00", "VpcId": "vpc-73c3cd17"}}, "comments": {"input": {}, "output": {}}, "description": "Creates a snapshot of a non-clustered Redis cluster that has only three nodes, primary and two read-replicas. CacheClusterId must be a specific node in the cluster.", "id": "createsnapshot-1474999681024", "title": "CreateSnapshot - NonClustered Redis, 2 read-replicas"}, {"input": {"ReplicationGroupId": "clusteredredis", "SnapshotName": "snapshot-2x5"}, "output": {"Snapshot": {"AutoMinorVersionUpgrade": true, "AutomaticFailover": "enabled", "CacheNodeType": "cache.m3.medium", "CacheParameterGroupName": "default.redis3.2.cluster.on", "CacheSubnetGroupName": "default", "Engine": "redis", "EngineVersion": "3.2.4", "NodeSnapshots": [{"CacheSize": "", "NodeGroupId": "0001"}, {"CacheSize": "", "NodeGroupId": "0002"}], "NumNodeGroups": 2, "Port": 6379, "PreferredMaintenanceWindow": "mon:09:30-mon:10:30", "ReplicationGroupDescription": "Redis cluster with 2 shards.", "ReplicationGroupId": "clusteredredis", "SnapshotName": "snapshot-2x5", "SnapshotRetentionLimit": 1, "SnapshotSource": "manual", "SnapshotStatus": "creating", "SnapshotWindow": "12:00-13:00", "VpcId": "vpc-73c3cd17"}}, "comments": {"input": {}, "output": {}}, "description": "Creates a snapshot of a clustered Redis cluster that has 2 shards, each with a primary and 4 read-replicas.", "id": "createsnapshot-clustered-redis-1486144841758", "title": "CreateSnapshot-clustered Redis"}], "DeleteCacheCluster": [{"input": {"CacheClusterId": "my-memcached"}, "output": {"CacheCluster": {"AutoMinorVersionUpgrade": true, "CacheClusterCreateTime": "2016-12-22T16:05:17.314Z", "CacheClusterId": "my-memcached", "CacheClusterStatus": "deleting", "CacheNodeType": "cache.r3.large", "CacheParameterGroup": {"CacheNodeIdsToReboot": [], "CacheParameterGroupName": "default.memcached1.4", "ParameterApplyStatus": "in-sync"}, "CacheSecurityGroups": [], "CacheSubnetGroupName": "default", "ClientDownloadLandingPage": "https://console.aws.amazon.com/elasticache/home#client-download:", "ConfigurationEndpoint": {"Address": "my-memcached2.ameaqx.cfg.use1.cache.amazonaws.com", "Port": 11211}, "Engine": "memcached", "EngineVersion": "1.4.24", "NumCacheNodes": 2, "PendingModifiedValues": {}, "PreferredAvailabilityZone": "Multiple", "PreferredMaintenanceWindow": "tue:07:30-tue:08:30"}}, "comments": {"input": {}, "output": {}}, "description": "Deletes an Amazon ElastiCache cluster.", "id": "deletecachecluster-1475010605291", "title": "DeleteCacheCluster"}], "DeleteCacheParameterGroup": [{"input": {"CacheParameterGroupName": "custom-mem1-4"}, "comments": {"input": {}, "output": {}}, "description": "Deletes the Amazon ElastiCache parameter group custom-mem1-4.", "id": "deletecacheparametergroup-1475010933957", "title": "DeleteCacheParameterGroup"}], "DeleteCacheSecurityGroup": [{"input": {"CacheSecurityGroupName": "my-sec-group"}, "comments": {"input": {}, "output": {}}, "description": "Deletes a cache security group.", "id": "deletecachesecuritygroup-1483046967507", "title": "DeleteCacheSecurityGroup"}], "DeleteCacheSubnetGroup": [{"input": {"CacheSubnetGroupName": "my-subnet-group"}, "comments": {"input": {}, "output": {}}, "description": "Deletes the Amazon ElastiCache subnet group my-subnet-group.", "id": "deletecachesubnetgroup-1475011431325", "title": "DeleteCacheSubnetGroup"}], "DeleteReplicationGroup": [{"input": {"ReplicationGroupId": "my-redis-rg", "RetainPrimaryCluster": false}, "output": {"ReplicationGroup": {"AutomaticFailover": "disabled", "Description": "simple redis cluster", "PendingModifiedValues": {}, "ReplicationGroupId": "my-redis-rg", "Status": "deleting"}}, "comments": {"input": {}, "output": {}}, "description": "Deletes the Amazon ElastiCache replication group my-redis-rg.", "id": "deletereplicationgroup-1475011641804", "title": "DeleteReplicationGroup"}], "DeleteSnapshot": [{"input": {"SnapshotName": "snapshot-20161212"}, "output": {"Snapshot": {"AutoMinorVersionUpgrade": true, "CacheClusterCreateTime": "2016-12-21T22:27:12.543Z", "CacheClusterId": "my-redis5", "CacheNodeType": "cache.m3.large", "CacheParameterGroupName": "default.redis3.2", "CacheSubnetGroupName": "default", "Engine": "redis", "EngineVersion": "3.2.4", "NodeSnapshots": [{"CacheNodeCreateTime": "2016-12-21T22:27:12.543Z", "CacheNodeId": "0001", "CacheSize": "3 MB", "SnapshotCreateTime": "2016-12-21T22:30:26Z"}], "NumCacheNodes": 1, "Port": 6379, "PreferredAvailabilityZone": "us-east-1c", "PreferredMaintenanceWindow": "fri:05:30-fri:06:30", "SnapshotName": "snapshot-20161212", "SnapshotRetentionLimit": 7, "SnapshotSource": "manual", "SnapshotStatus": "deleting", "SnapshotWindow": "10:00-11:00", "VpcId": "vpc-91280df6"}}, "comments": {"input": {}, "output": {}}, "description": "Deletes the Redis snapshot snapshot-20160822.", "id": "deletesnapshot-1475011945779", "title": "DeleteSnapshot"}], "DescribeCacheClusters": [{"input": {"CacheClusterId": "my-mem-cluster"}, "output": {"CacheClusters": [{"AutoMinorVersionUpgrade": true, "CacheClusterCreateTime": "2016-12-21T21:59:43.794Z", "CacheClusterId": "my-mem-cluster", "CacheClusterStatus": "available", "CacheNodeType": "cache.t2.medium", "CacheParameterGroup": {"CacheNodeIdsToReboot": [], "CacheParameterGroupName": "default.memcached1.4", "ParameterApplyStatus": "in-sync"}, "CacheSecurityGroups": [], "CacheSubnetGroupName": "default", "ClientDownloadLandingPage": "https://console.aws.amazon.com/elasticache/home#client-download:", "ConfigurationEndpoint": {"Address": "my-mem-cluster.abcdef.cfg.use1.cache.amazonaws.com", "Port": 11211}, "Engine": "memcached", "EngineVersion": "1.4.24", "NumCacheNodes": 2, "PendingModifiedValues": {}, "PreferredAvailabilityZone": "Multiple", "PreferredMaintenanceWindow": "wed:06:00-wed:07:00"}]}, "comments": {"input": {}, "output": {}}, "description": "Lists the details for up to 50 cache clusters.", "id": "describecacheclusters-1475012269754", "title": "DescribeCacheClusters"}, {"input": {"CacheClusterId": "my-mem-cluster", "ShowCacheNodeInfo": true}, "output": {"CacheClusters": [{"AutoMinorVersionUpgrade": true, "CacheClusterCreateTime": "2016-12-21T21:59:43.794Z", "CacheClusterId": "my-mem-cluster", "CacheClusterStatus": "available", "CacheNodeType": "cache.t2.medium", "CacheNodes": [{"CacheNodeCreateTime": "2016-12-21T21:59:43.794Z", "CacheNodeId": "0001", "CacheNodeStatus": "available", "CustomerAvailabilityZone": "us-east-1b", "Endpoint": {"Address": "my-mem-cluster.ameaqx.0001.use1.cache.amazonaws.com", "Port": 11211}, "ParameterGroupStatus": "in-sync"}, {"CacheNodeCreateTime": "2016-12-21T21:59:43.794Z", "CacheNodeId": "0002", "CacheNodeStatus": "available", "CustomerAvailabilityZone": "us-east-1a", "Endpoint": {"Address": "my-mem-cluster.ameaqx.0002.use1.cache.amazonaws.com", "Port": 11211}, "ParameterGroupStatus": "in-sync"}], "CacheParameterGroup": {"CacheNodeIdsToReboot": [], "CacheParameterGroupName": "default.memcached1.4", "ParameterApplyStatus": "in-sync"}, "CacheSecurityGroups": [], "CacheSubnetGroupName": "default", "ClientDownloadLandingPage": "https://console.aws.amazon.com/elasticache/home#client-download:", "ConfigurationEndpoint": {"Address": "my-mem-cluster.ameaqx.cfg.use1.cache.amazonaws.com", "Port": 11211}, "Engine": "memcached", "EngineVersion": "1.4.24", "NumCacheNodes": 2, "PendingModifiedValues": {}, "PreferredAvailabilityZone": "Multiple", "PreferredMaintenanceWindow": "wed:06:00-wed:07:00"}]}, "comments": {"input": {}, "output": {}}, "description": "Lists the details for the cache cluster my-mem-cluster.", "id": "describecacheclusters-1475012269754", "title": "DescribeCacheClusters"}], "DescribeCacheEngineVersions": [{"input": {}, "output": {"CacheEngineVersions": [{"CacheEngineDescription": "memcached", "CacheEngineVersionDescription": "memcached version 1.4.14", "CacheParameterGroupFamily": "memcached1.4", "Engine": "memcached", "EngineVersion": "1.4.14"}, {"CacheEngineDescription": "memcached", "CacheEngineVersionDescription": "memcached version 1.4.24", "CacheParameterGroupFamily": "memcached1.4", "Engine": "memcached", "EngineVersion": "1.4.24"}, {"CacheEngineDescription": "memcached", "CacheEngineVersionDescription": "memcached version 1.4.33", "CacheParameterGroupFamily": "memcached1.4", "Engine": "memcached", "EngineVersion": "1.4.33"}, {"CacheEngineDescription": "memcached", "CacheEngineVersionDescription": "memcached version 1.4.5", "CacheParameterGroupFamily": "memcached1.4", "Engine": "memcached", "EngineVersion": "1.4.5"}, {"CacheEngineDescription": "Redis", "CacheEngineVersionDescription": "redis version 2.6.13", "CacheParameterGroupFamily": "redis2.6", "Engine": "redis", "EngineVersion": "2.6.13"}, {"CacheEngineDescription": "Redis", "CacheEngineVersionDescription": "redis version 2.8.19", "CacheParameterGroupFamily": "redis2.8", "Engine": "redis", "EngineVersion": "2.8.19"}, {"CacheEngineDescription": "Redis", "CacheEngineVersionDescription": "redis version 2.8.21", "CacheParameterGroupFamily": "redis2.8", "Engine": "redis", "EngineVersion": "2.8.21"}, {"CacheEngineDescription": "Redis", "CacheEngineVersionDescription": "redis version 2.8.22 R5", "CacheParameterGroupFamily": "redis2.8", "Engine": "redis", "EngineVersion": "2.8.22"}, {"CacheEngineDescription": "Redis", "CacheEngineVersionDescription": "redis version 2.8.23 R4", "CacheParameterGroupFamily": "redis2.8", "Engine": "redis", "EngineVersion": "2.8.23"}, {"CacheEngineDescription": "Redis", "CacheEngineVersionDescription": "redis version 2.8.24 R3", "CacheParameterGroupFamily": "redis2.8", "Engine": "redis", "EngineVersion": "2.8.24"}, {"CacheEngineDescription": "Redis", "CacheEngineVersionDescription": "redis version 2.8.6", "CacheParameterGroupFamily": "redis2.8", "Engine": "redis", "EngineVersion": "2.8.6"}, {"CacheEngineDescription": "Redis", "CacheEngineVersionDescription": "redis version 3.2.4", "CacheParameterGroupFamily": "redis3.2", "Engine": "redis", "EngineVersion": "3.2.4"}]}, "comments": {"input": {}, "output": {}}, "description": "Lists the details for up to 25 Memcached and Redis cache engine versions.", "id": "describecacheengineversions-1475012638790", "title": "DescribeCacheEngineVersions"}, {"input": {"DefaultOnly": false, "Engine": "redis", "MaxRecords": 50}, "output": {"CacheEngineVersions": [{"CacheEngineDescription": "Redis", "CacheEngineVersionDescription": "redis version 2.6.13", "CacheParameterGroupFamily": "redis2.6", "Engine": "redis", "EngineVersion": "2.6.13"}, {"CacheEngineDescription": "Redis", "CacheEngineVersionDescription": "redis version 2.8.19", "CacheParameterGroupFamily": "redis2.8", "Engine": "redis", "EngineVersion": "2.8.19"}, {"CacheEngineDescription": "Redis", "CacheEngineVersionDescription": "redis version 2.8.21", "CacheParameterGroupFamily": "redis2.8", "Engine": "redis", "EngineVersion": "2.8.21"}, {"CacheEngineDescription": "Redis", "CacheEngineVersionDescription": "redis version 2.8.22 R5", "CacheParameterGroupFamily": "redis2.8", "Engine": "redis", "EngineVersion": "2.8.22"}, {"CacheEngineDescription": "Redis", "CacheEngineVersionDescription": "redis version 2.8.23 R4", "CacheParameterGroupFamily": "redis2.8", "Engine": "redis", "EngineVersion": "2.8.23"}, {"CacheEngineDescription": "Redis", "CacheEngineVersionDescription": "redis version 2.8.24 R3", "CacheParameterGroupFamily": "redis2.8", "Engine": "redis", "EngineVersion": "2.8.24"}, {"CacheEngineDescription": "Redis", "CacheEngineVersionDescription": "redis version 2.8.6", "CacheParameterGroupFamily": "redis2.8", "Engine": "redis", "EngineVersion": "2.8.6"}, {"CacheEngineDescription": "Redis", "CacheEngineVersionDescription": "redis version 3.2.4", "CacheParameterGroupFamily": "redis3.2", "Engine": "redis", "EngineVersion": "3.2.4"}], "Marker": ""}, "comments": {"input": {}, "output": {}}, "description": "Lists the details for up to 50 Redis cache engine versions.", "id": "describecacheengineversions-1475012638790", "title": "DescribeCacheEngineVersions"}], "DescribeCacheParameterGroups": [{"input": {"CacheParameterGroupName": "custom-mem1-4"}, "output": {"CacheParameterGroups": [{"CacheParameterGroupFamily": "memcached1.4", "CacheParameterGroupName": "custom-mem1-4", "Description": "Custom memcache param group"}]}, "comments": {"input": {}, "output": {}}, "description": "Returns a list of cache parameter group descriptions. If a cache parameter group name is specified, the list contains only the descriptions for that group.", "id": "describecacheparametergroups-1483045457557", "title": "DescribeCacheParameterGroups"}], "DescribeCacheParameters": [{"input": {"CacheParameterGroupName": "custom-redis2-8", "MaxRecords": 100, "Source": "user"}, "output": {"Marker": "", "Parameters": [{"AllowedValues": "yes,no", "ChangeType": "requires-reboot", "DataType": "string", "Description": "Apply rehashing or not.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "activerehashing", "ParameterValue": "yes", "Source": "system"}, {"AllowedValues": "always,everysec,no", "ChangeType": "immediate", "DataType": "string", "Description": "fsync policy for AOF persistence", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "appendfsync", "ParameterValue": "everysec", "Source": "system"}, {"AllowedValues": "yes,no", "ChangeType": "immediate", "DataType": "string", "Description": "Enable Redis persistence.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "appendonly", "ParameterValue": "no", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Normal client output buffer hard limit in bytes.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "client-output-buffer-limit-normal-hard-limit", "ParameterValue": "0", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Normal client output buffer soft limit in bytes.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "client-output-buffer-limit-normal-soft-limit", "ParameterValue": "0", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Normal client output buffer soft limit in seconds.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "client-output-buffer-limit-normal-soft-seconds", "ParameterValue": "0", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Pubsub client output buffer hard limit in bytes.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "client-output-buffer-limit-pubsub-hard-limit", "ParameterValue": "33554432", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Pubsub client output buffer soft limit in bytes.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "client-output-buffer-limit-pubsub-soft-limit", "ParameterValue": "8388608", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Pubsub client output buffer soft limit in seconds.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "client-output-buffer-limit-pubsub-soft-seconds", "ParameterValue": "60", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Slave client output buffer soft limit in seconds.", "IsModifiable": false, "MinimumEngineVersion": "2.8.6", "ParameterName": "client-output-buffer-limit-slave-soft-seconds", "ParameterValue": "60", "Source": "system"}, {"AllowedValues": "yes,no", "ChangeType": "immediate", "DataType": "string", "Description": "If enabled, clients who attempt to write to a read-only slave will be disconnected. Applicable to 2.8.23 and higher.", "IsModifiable": true, "MinimumEngineVersion": "2.8.23", "ParameterName": "close-on-slave-write", "ParameterValue": "yes", "Source": "system"}, {"AllowedValues": "1-1200000", "ChangeType": "requires-reboot", "DataType": "integer", "Description": "Set the number of databases.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "databases", "ParameterValue": "16", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "The maximum number of hash entries in order for the dataset to be compressed.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "hash-max-ziplist-entries", "ParameterValue": "512", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "The threshold of biggest hash entries in order for the dataset to be compressed.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "hash-max-ziplist-value", "ParameterValue": "64", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "The maximum number of list entries in order for the dataset to be compressed.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "list-max-ziplist-entries", "ParameterValue": "512", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "The threshold of biggest list entries in order for the dataset to be compressed.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "list-max-ziplist-value", "ParameterValue": "64", "Source": "system"}, {"AllowedValues": "5000", "ChangeType": "immediate", "DataType": "integer", "Description": "Max execution time of a Lua script in milliseconds. 0 for unlimited execution without warnings.", "IsModifiable": false, "MinimumEngineVersion": "2.8.6", "ParameterName": "lua-time-limit", "ParameterValue": "5000", "Source": "system"}, {"AllowedValues": "1-65000", "ChangeType": "requires-reboot", "DataType": "integer", "Description": "The maximum number of Redis clients.", "IsModifiable": false, "MinimumEngineVersion": "2.8.6", "ParameterName": "maxclients", "ParameterValue": "65000", "Source": "system"}, {"AllowedValues": "volatile-lru,allkeys-lru,volatile-random,allkeys-random,volatile-ttl,noeviction", "ChangeType": "immediate", "DataType": "string", "Description": "Max memory policy.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "maxmemory-policy", "ParameterValue": "volatile-lru", "Source": "system"}, {"AllowedValues": "1-", "ChangeType": "immediate", "DataType": "integer", "Description": "Max memory samples.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "maxmemory-samples", "ParameterValue": "3", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Maximum number of seconds within which the master must receive a ping from a slave to take writes. Use this parameter together with min-slaves-to-write to regulate when the master stops accepting writes. Setting this value to 0 means the master always takes writes.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "min-slaves-max-lag", "ParameterValue": "10", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Number of slaves that must be connected in order for master to take writes. Use this parameter together with min-slaves-max-lag to regulate when the master stops accepting writes. Setting this to 0 means the master always takes writes.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "min-slaves-to-write", "ParameterValue": "0", "Source": "system"}, {"ChangeType": "immediate", "DataType": "string", "Description": "The keyspace events for Redis to notify Pub/Sub clients about. By default all notifications are disabled", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "notify-keyspace-events", "Source": "system"}, {"AllowedValues": "16384-", "ChangeType": "immediate", "DataType": "integer", "Description": "The replication backlog size in bytes for PSYNC. This is the size of the buffer which accumulates slave data when slave is disconnected for some time, so that when slave reconnects again, only transfer the portion of data which the slave missed. Minimum value is 16K.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "repl-backlog-size", "ParameterValue": "1048576", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "The amount of time in seconds after the master no longer have any slaves connected for the master to free the replication backlog. A value of 0 means to never release the backlog.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "repl-backlog-ttl", "ParameterValue": "3600", "Source": "system"}, {"AllowedValues": "11-", "ChangeType": "immediate", "DataType": "integer", "Description": "The timeout in seconds for bulk transfer I/O during sync and master timeout from the perspective of the slave, and slave timeout from the perspective of the master.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "repl-timeout", "ParameterValue": "60", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "The amount of memory reserved for non-cache memory usage, in bytes. You may want to increase this parameter for nodes with read replicas, AOF enabled, etc, to reduce swap usage.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "reserved-memory", "ParameterValue": "0", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "The limit in the size of the set in order for the dataset to be compressed.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "set-max-intset-entries", "ParameterValue": "512", "Source": "system"}, {"AllowedValues": "yes,no", "ChangeType": "immediate", "DataType": "string", "Description": "Configures if chaining of slaves is allowed", "IsModifiable": false, "MinimumEngineVersion": "2.8.6", "ParameterName": "slave-allow-chaining", "ParameterValue": "no", "Source": "system"}, {"AllowedValues": "-", "ChangeType": "immediate", "DataType": "integer", "Description": "The execution time, in microseconds, to exceed in order for the command to get logged. Note that a negative number disables the slow log, while a value of zero forces the logging of every command.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "slowlog-log-slower-than", "ParameterValue": "10000", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "The length of the slow log. There is no limit to this length. Just be aware that it will consume memory. You can reclaim memory used by the slow log with SLOWLOG RESET.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "slowlog-max-len", "ParameterValue": "128", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "If non-zero, send ACKs every given number of seconds.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "tcp-keepalive", "ParameterValue": "0", "Source": "system"}, {"AllowedValues": "0,20-", "ChangeType": "immediate", "DataType": "integer", "Description": "Close connection if client is idle for a given number of seconds, or never if 0.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "timeout", "ParameterValue": "0", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "The maximum number of sorted set entries in order for the dataset to be compressed.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "zset-max-ziplist-entries", "ParameterValue": "128", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "The threshold of biggest sorted set entries in order for the dataset to be compressed.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "zset-max-ziplist-value", "ParameterValue": "64", "Source": "system"}]}, "comments": {"input": {}, "output": {}}, "description": "Lists up to 100 user parameter values for the parameter group custom.redis2.8.", "id": "describecacheparameters-1475013576900", "title": "DescribeCacheParameters"}], "DescribeCacheSecurityGroups": [{"input": {"CacheSecurityGroupName": "my-sec-group"}, "comments": {"input": {}, "output": {}}, "description": "Returns a list of cache security group descriptions. If a cache security group name is specified, the list contains only the description of that group.", "id": "describecachesecuritygroups-1483047200801", "title": "DescribeCacheSecurityGroups"}], "DescribeCacheSubnetGroups": [{"input": {"MaxRecords": 25}, "output": {"CacheSubnetGroups": [{"CacheSubnetGroupDescription": "Default CacheSubnetGroup", "CacheSubnetGroupName": "default", "Subnets": [{"SubnetAvailabilityZone": {"Name": "us-east-1a"}, "SubnetIdentifier": "subnet-1a2b3c4d"}, {"SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetIdentifier": "subnet-a1b2c3d4"}, {"SubnetAvailabilityZone": {"Name": "us-east-1e"}, "SubnetIdentifier": "subnet-abcd1234"}, {"SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetIdentifier": "subnet-1234abcd"}], "VpcId": "vpc-91280df6"}], "Marker": ""}, "comments": {"input": {}, "output": {}}, "description": "Describes up to 25 cache subnet groups.", "id": "describecachesubnetgroups-1482439214064", "title": "DescribeCacheSubnetGroups"}], "DescribeEngineDefaultParameters": [{"input": {"CacheParameterGroupFamily": "redis2.8", "MaxRecords": 25}, "output": {"EngineDefaults": {"CacheNodeTypeSpecificParameters": [{"AllowedValues": "0-", "CacheNodeTypeSpecificValues": [{"CacheNodeType": "cache.c1.xlarge", "Value": "650117120"}, {"CacheNodeType": "cache.m1.large", "Value": "702545920"}, {"CacheNodeType": "cache.m1.medium", "Value": "309329920"}, {"CacheNodeType": "cache.m1.small", "Value": "94371840"}, {"CacheNodeType": "cache.m1.xlarge", "Value": "1488977920"}, {"CacheNodeType": "cache.m2.2xlarge", "Value": "3502243840"}, {"CacheNodeType": "cache.m2.4xlarge", "Value": "7088373760"}, {"CacheNodeType": "cache.m2.xlarge", "Value": "1709178880"}, {"CacheNodeType": "cache.m3.2xlarge", "Value": "2998927360"}, {"CacheNodeType": "cache.m3.large", "Value": "650117120"}, {"CacheNodeType": "cache.m3.medium", "Value": "309329920"}, {"CacheNodeType": "cache.m3.xlarge", "Value": "1426063360"}, {"CacheNodeType": "cache.m4.10xlarge", "Value": "16604761424"}, {"CacheNodeType": "cache.m4.2xlarge", "Value": "3188912636"}, {"CacheNodeType": "cache.m4.4xlarge", "Value": "6525729063"}, {"CacheNodeType": "cache.m4.large", "Value": "689259315"}, {"CacheNodeType": "cache.m4.xlarge", "Value": "1532850176"}, {"CacheNodeType": "cache.r3.2xlarge", "Value": "6081740800"}, {"CacheNodeType": "cache.r3.4xlarge", "Value": "12268339200"}, {"CacheNodeType": "cache.r3.8xlarge", "Value": "24536678400"}, {"CacheNodeType": "cache.r3.large", "Value": "1468006400"}, {"CacheNodeType": "cache.r3.xlarge", "Value": "3040870400"}, {"CacheNodeType": "cache.t1.micro", "Value": "14260633"}, {"CacheNodeType": "cache.t2.medium", "Value": "346134937"}, {"CacheNodeType": "cache.t2.micro", "Value": "58195968"}, {"CacheNodeType": "cache.t2.small", "Value": "166513868"}], "ChangeType": "immediate", "DataType": "integer", "Description": "Slave client output buffer hard limit in bytes.", "IsModifiable": false, "MinimumEngineVersion": "2.8.6", "ParameterName": "client-output-buffer-limit-slave-hard-limit", "Source": "system"}, {"AllowedValues": "0-", "CacheNodeTypeSpecificValues": [{"CacheNodeType": "cache.c1.xlarge", "Value": "650117120"}, {"CacheNodeType": "cache.m1.large", "Value": "702545920"}, {"CacheNodeType": "cache.m1.medium", "Value": "309329920"}, {"CacheNodeType": "cache.m1.small", "Value": "94371840"}, {"CacheNodeType": "cache.m1.xlarge", "Value": "1488977920"}, {"CacheNodeType": "cache.m2.2xlarge", "Value": "3502243840"}, {"CacheNodeType": "cache.m2.4xlarge", "Value": "7088373760"}, {"CacheNodeType": "cache.m2.xlarge", "Value": "1709178880"}, {"CacheNodeType": "cache.m3.2xlarge", "Value": "2998927360"}, {"CacheNodeType": "cache.m3.large", "Value": "650117120"}, {"CacheNodeType": "cache.m3.medium", "Value": "309329920"}, {"CacheNodeType": "cache.m3.xlarge", "Value": "1426063360"}, {"CacheNodeType": "cache.m4.10xlarge", "Value": "16604761424"}, {"CacheNodeType": "cache.m4.2xlarge", "Value": "3188912636"}, {"CacheNodeType": "cache.m4.4xlarge", "Value": "6525729063"}, {"CacheNodeType": "cache.m4.large", "Value": "689259315"}, {"CacheNodeType": "cache.m4.xlarge", "Value": "1532850176"}, {"CacheNodeType": "cache.r3.2xlarge", "Value": "6081740800"}, {"CacheNodeType": "cache.r3.4xlarge", "Value": "12268339200"}, {"CacheNodeType": "cache.r3.8xlarge", "Value": "24536678400"}, {"CacheNodeType": "cache.r3.large", "Value": "1468006400"}, {"CacheNodeType": "cache.r3.xlarge", "Value": "3040870400"}, {"CacheNodeType": "cache.t1.micro", "Value": "14260633"}, {"CacheNodeType": "cache.t2.medium", "Value": "346134937"}, {"CacheNodeType": "cache.t2.micro", "Value": "58195968"}, {"CacheNodeType": "cache.t2.small", "Value": "166513868"}], "ChangeType": "immediate", "DataType": "integer", "Description": "Slave client output buffer soft limit in bytes.", "IsModifiable": false, "MinimumEngineVersion": "2.8.6", "ParameterName": "client-output-buffer-limit-slave-soft-limit", "Source": "system"}, {"AllowedValues": "0-", "CacheNodeTypeSpecificValues": [{"CacheNodeType": "cache.c1.xlarge", "Value": "6501171200"}, {"CacheNodeType": "cache.m1.large", "Value": "7025459200"}, {"CacheNodeType": "cache.m1.medium", "Value": "3093299200"}, {"CacheNodeType": "cache.m1.small", "Value": "943718400"}, {"CacheNodeType": "cache.m1.xlarge", "Value": "14889779200"}, {"CacheNodeType": "cache.m2.2xlarge", "Value": "35022438400"}, {"CacheNodeType": "cache.m2.4xlarge", "Value": "70883737600"}, {"CacheNodeType": "cache.m2.xlarge", "Value": "17091788800"}, {"CacheNodeType": "cache.m3.2xlarge", "Value": "29989273600"}, {"CacheNodeType": "cache.m3.large", "Value": "6501171200"}, {"CacheNodeType": "cache.m3.medium", "Value": "2988441600"}, {"CacheNodeType": "cache.m3.xlarge", "Value": "14260633600"}, {"CacheNodeType": "cache.m4.10xlarge", "Value": "166047614239"}, {"CacheNodeType": "cache.m4.2xlarge", "Value": "31889126359"}, {"CacheNodeType": "cache.m4.4xlarge", "Value": "65257290629"}, {"CacheNodeType": "cache.m4.large", "Value": "6892593152"}, {"CacheNodeType": "cache.m4.xlarge", "Value": "15328501760"}, {"CacheNodeType": "cache.r3.2xlarge", "Value": "62495129600"}, {"CacheNodeType": "cache.r3.4xlarge", "Value": "126458265600"}, {"CacheNodeType": "cache.r3.8xlarge", "Value": "254384537600"}, {"CacheNodeType": "cache.r3.large", "Value": "14470348800"}, {"CacheNodeType": "cache.r3.xlarge", "Value": "30513561600"}, {"CacheNodeType": "cache.t1.micro", "Value": "142606336"}, {"CacheNodeType": "cache.t2.medium", "Value": "3461349376"}, {"CacheNodeType": "cache.t2.micro", "Value": "581959680"}, {"CacheNodeType": "cache.t2.small", "Value": "1665138688"}], "ChangeType": "immediate", "DataType": "integer", "Description": "The maximum configurable amount of memory to use to store items, in bytes.", "IsModifiable": false, "MinimumEngineVersion": "2.8.6", "ParameterName": "maxmemory", "Source": "system"}], "CacheParameterGroupFamily": "redis2.8", "Marker": "bWluLXNsYXZlcy10by13cml0ZQ==", "Parameters": [{"AllowedValues": "yes,no", "ChangeType": "requires-reboot", "DataType": "string", "Description": "Apply rehashing or not.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "activerehashing", "ParameterValue": "yes", "Source": "system"}, {"AllowedValues": "always,everysec,no", "ChangeType": "immediate", "DataType": "string", "Description": "fsync policy for AOF persistence", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "appendfsync", "ParameterValue": "everysec", "Source": "system"}, {"AllowedValues": "yes,no", "ChangeType": "immediate", "DataType": "string", "Description": "Enable Redis persistence.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "appendonly", "ParameterValue": "no", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Normal client output buffer hard limit in bytes.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "client-output-buffer-limit-normal-hard-limit", "ParameterValue": "0", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Normal client output buffer soft limit in bytes.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "client-output-buffer-limit-normal-soft-limit", "ParameterValue": "0", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Normal client output buffer soft limit in seconds.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "client-output-buffer-limit-normal-soft-seconds", "ParameterValue": "0", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Pubsub client output buffer hard limit in bytes.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "client-output-buffer-limit-pubsub-hard-limit", "ParameterValue": "33554432", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Pubsub client output buffer soft limit in bytes.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "client-output-buffer-limit-pubsub-soft-limit", "ParameterValue": "8388608", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Pubsub client output buffer soft limit in seconds.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "client-output-buffer-limit-pubsub-soft-seconds", "ParameterValue": "60", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Slave client output buffer soft limit in seconds.", "IsModifiable": false, "MinimumEngineVersion": "2.8.6", "ParameterName": "client-output-buffer-limit-slave-soft-seconds", "ParameterValue": "60", "Source": "system"}, {"AllowedValues": "yes,no", "ChangeType": "immediate", "DataType": "string", "Description": "If enabled, clients who attempt to write to a read-only slave will be disconnected. Applicable to 2.8.23 and higher.", "IsModifiable": true, "MinimumEngineVersion": "2.8.23", "ParameterName": "close-on-slave-write", "ParameterValue": "yes", "Source": "system"}, {"AllowedValues": "1-1200000", "ChangeType": "requires-reboot", "DataType": "integer", "Description": "Set the number of databases.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "databases", "ParameterValue": "16", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "The maximum number of hash entries in order for the dataset to be compressed.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "hash-max-ziplist-entries", "ParameterValue": "512", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "The threshold of biggest hash entries in order for the dataset to be compressed.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "hash-max-ziplist-value", "ParameterValue": "64", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "The maximum number of list entries in order for the dataset to be compressed.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "list-max-ziplist-entries", "ParameterValue": "512", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "The threshold of biggest list entries in order for the dataset to be compressed.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "list-max-ziplist-value", "ParameterValue": "64", "Source": "system"}, {"AllowedValues": "5000", "ChangeType": "immediate", "DataType": "integer", "Description": "Max execution time of a Lua script in milliseconds. 0 for unlimited execution without warnings.", "IsModifiable": false, "MinimumEngineVersion": "2.8.6", "ParameterName": "lua-time-limit", "ParameterValue": "5000", "Source": "system"}, {"AllowedValues": "1-65000", "ChangeType": "requires-reboot", "DataType": "integer", "Description": "The maximum number of Redis clients.", "IsModifiable": false, "MinimumEngineVersion": "2.8.6", "ParameterName": "maxclients", "ParameterValue": "65000", "Source": "system"}, {"AllowedValues": "volatile-lru,allkeys-lru,volatile-random,allkeys-random,volatile-ttl,noeviction", "ChangeType": "immediate", "DataType": "string", "Description": "Max memory policy.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "maxmemory-policy", "ParameterValue": "volatile-lru", "Source": "system"}, {"AllowedValues": "1-", "ChangeType": "immediate", "DataType": "integer", "Description": "Max memory samples.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "maxmemory-samples", "ParameterValue": "3", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Maximum number of seconds within which the master must receive a ping from a slave to take writes. Use this parameter together with min-slaves-to-write to regulate when the master stops accepting writes. Setting this value to 0 means the master always takes writes.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "min-slaves-max-lag", "ParameterValue": "10", "Source": "system"}, {"AllowedValues": "0-", "ChangeType": "immediate", "DataType": "integer", "Description": "Number of slaves that must be connected in order for master to take writes. Use this parameter together with min-slaves-max-lag to regulate when the master stops accepting writes. Setting this to 0 means the master always takes writes.", "IsModifiable": true, "MinimumEngineVersion": "2.8.6", "ParameterName": "min-slaves-to-write", "ParameterValue": "0", "Source": "system"}]}}, "comments": {"input": {}, "output": {}}, "description": "Returns the default engine and system parameter information for the specified cache engine.", "id": "describeenginedefaultparameters-1481738057686", "title": "DescribeEngineDefaultParameters"}], "DescribeEvents": [{"input": {"Duration": 360, "SourceType": "cache-cluster"}, "output": {"Events": [{"Date": "2016-12-22T16:27:56.088Z", "Message": "Added cache node 0001 in availability zone us-east-1e", "SourceIdentifier": "redis-cluster", "SourceType": "cache-cluster"}, {"Date": "2016-12-22T16:27:56.078Z", "Message": "Cache cluster created", "SourceIdentifier": "redis-cluster", "SourceType": "cache-cluster"}, {"Date": "2016-12-22T16:05:17.326Z", "Message": "Added cache node 0002 in availability zone us-east-1c", "SourceIdentifier": "my-memcached2", "SourceType": "cache-cluster"}, {"Date": "2016-12-22T16:05:17.323Z", "Message": "Added cache node 0001 in availability zone us-east-1e", "SourceIdentifier": "my-memcached2", "SourceType": "cache-cluster"}, {"Date": "2016-12-22T16:05:17.314Z", "Message": "Cache cluster created", "SourceIdentifier": "my-memcached2", "SourceType": "cache-cluster"}], "Marker": ""}, "comments": {"input": {}, "output": {}}, "description": "Describes all the cache-cluster events for the past 120 minutes.", "id": "describeevents-1481843894757", "title": "DescribeEvents"}, {"input": {"StartTime": "2016-12-22T15:00:00.000Z"}, "output": {"Events": [{"Date": "2016-12-22T21:35:46.674Z", "Message": "Snapshot succeeded for snapshot with ID 'cr-bkup' of replication group with ID 'clustered-redis'", "SourceIdentifier": "clustered-redis-0001-001", "SourceType": "cache-cluster"}, {"Date": "2016-12-22T16:27:56.088Z", "Message": "Added cache node 0001 in availability zone us-east-1e", "SourceIdentifier": "redis-cluster", "SourceType": "cache-cluster"}, {"Date": "2016-12-22T16:27:56.078Z", "Message": "Cache cluster created", "SourceIdentifier": "redis-cluster", "SourceType": "cache-cluster"}, {"Date": "2016-12-22T16:05:17.326Z", "Message": "Added cache node 0002 in availability zone us-east-1c", "SourceIdentifier": "my-memcached2", "SourceType": "cache-cluster"}, {"Date": "2016-12-22T16:05:17.323Z", "Message": "Added cache node 0001 in availability zone us-east-1e", "SourceIdentifier": "my-memcached2", "SourceType": "cache-cluster"}, {"Date": "2016-12-22T16:05:17.314Z", "Message": "Cache cluster created", "SourceIdentifier": "my-memcached2", "SourceType": "cache-cluster"}], "Marker": ""}, "comments": {"input": {}, "output": {}}, "description": "Describes all the replication-group events from 3:00P to 5:00P on November 11, 2016.", "id": "describeevents-1481843894757", "title": "DescribeEvents"}], "DescribeReplicationGroups": [{"input": {}, "output": {"Marker": "", "ReplicationGroups": [{"AutomaticFailover": "enabled", "Description": "Test cluster", "MemberClusters": ["clustered-redis-0001-001", "clustered-redis-0001-002", "clustered-redis-0002-001", "clustered-redis-0002-002"], "NodeGroups": [{"NodeGroupId": "0001", "NodeGroupMembers": [{"CacheClusterId": "clustered-redis-0001-001", "CacheNodeId": "0001", "PreferredAvailabilityZone": "us-east-1e"}, {"CacheClusterId": "clustered-redis-0001-002", "CacheNodeId": "0001", "PreferredAvailabilityZone": "us-east-1c"}], "Status": "available"}, {"NodeGroupId": "0002", "NodeGroupMembers": [{"CacheClusterId": "clustered-redis-0002-001", "CacheNodeId": "0001", "PreferredAvailabilityZone": "us-east-1c"}, {"CacheClusterId": "clustered-redis-0002-002", "CacheNodeId": "0001", "PreferredAvailabilityZone": "us-east-1b"}], "Status": "available"}], "PendingModifiedValues": {}, "ReplicationGroupId": "clustered-redis", "Status": "available"}]}, "comments": {"input": {}, "output": {}}, "description": "Returns information about the replication group myreplgroup.", "id": "describereplicationgroups-*************", "title": "DescribeReplicationGroups"}], "DescribeReservedCacheNodes": [{"input": {"MaxRecords": 25}, "comments": {"input": {}, "output": {}}, "description": "Returns information about reserved cache nodes for this account, or about a specified reserved cache node. If the account has no reserved cache nodes, the operation returns an empty list, as shown here.", "id": "describereservedcachenodes-*************", "title": "DescribeReservedCacheNodes"}], "DescribeReservedCacheNodesOfferings": [{"input": {"MaxRecords": 20}, "output": {"Marker": "1ef01f5b-433f-94ff-a530-61a56bfc8e7a", "ReservedCacheNodesOfferings": [{"CacheNodeType": "cache.m1.small", "Duration": ********, "FixedPrice": 157.0, "OfferingType": "Medium Utilization", "ProductDescription": "memcached", "RecurringCharges": [], "ReservedCacheNodesOfferingId": "0167633d-37f6-4222-b872-b1f22eb79ba4", "UsagePrice": 0.017}, {"CacheNodeType": "cache.m4.xlarge", "Duration": ********, "FixedPrice": 1248.0, "OfferingType": "Heavy Utilization", "ProductDescription": "redis", "RecurringCharges": [{"RecurringChargeAmount": 0.077, "RecurringChargeFrequency": "Hourly"}], "ReservedCacheNodesOfferingId": "02c04e13-baca-4e71-9ceb-620eed94827d", "UsagePrice": 0.0}, {"CacheNodeType": "cache.m2.4xlarge", "Duration": ********, "FixedPrice": 2381.0, "OfferingType": "Medium Utilization", "ProductDescription": "memcached", "RecurringCharges": [], "ReservedCacheNodesOfferingId": "02e1755e-76e8-48e3-8d82-820a5726a458", "UsagePrice": 0.276}, {"CacheNodeType": "cache.m1.small", "Duration": ********, "FixedPrice": 188.0, "OfferingType": "Heavy Utilization", "ProductDescription": "redis", "RecurringCharges": [{"RecurringChargeAmount": 0.013, "RecurringChargeFrequency": "Hourly"}], "ReservedCacheNodesOfferingId": "03315215-7b87-421a-a3dd-785021e4113f", "UsagePrice": 0.0}, {"CacheNodeType": "cache.m4.10xlarge", "Duration": 31536000, "FixedPrice": 6158.0, "OfferingType": "Heavy Utilization", "ProductDescription": "redis", "RecurringCharges": [{"RecurringChargeAmount": 1.125, "RecurringChargeFrequency": "Hourly"}], "ReservedCacheNodesOfferingId": "05ffbb44-2ace-4476-a2a5-8ec99f866fb3", "UsagePrice": 0.0}, {"CacheNodeType": "cache.m1.small", "Duration": 31536000, "FixedPrice": 101.0, "OfferingType": "Medium Utilization", "ProductDescription": "redis", "RecurringCharges": [], "ReservedCacheNodesOfferingId": "065c71ae-4a4e-4f1e-bebf-37525f4c6cb2", "UsagePrice": 0.023}, {"CacheNodeType": "cache.m1.medium", "Duration": ********, "FixedPrice": 314.0, "OfferingType": "Medium Utilization", "ProductDescription": "memcached", "RecurringCharges": [], "ReservedCacheNodesOfferingId": "06774b12-7f5e-48c1-907a-f286c63f327d", "UsagePrice": 0.034}, {"CacheNodeType": "cache.m2.xlarge", "Duration": 31536000, "FixedPrice": 163.0, "OfferingType": "Light Utilization", "ProductDescription": "memcached", "RecurringCharges": [], "ReservedCacheNodesOfferingId": "0924ac6b-847f-4761-ba6b-4290b2adf719", "UsagePrice": 0.137}, {"CacheNodeType": "cache.m2.xlarge", "Duration": ********, "FixedPrice": 719.0, "OfferingType": "Heavy Utilization", "ProductDescription": "redis", "RecurringCharges": [{"RecurringChargeAmount": 0.049, "RecurringChargeFrequency": "Hourly"}], "ReservedCacheNodesOfferingId": "09eeb126-69b6-4d3f-8f94-ca3510629f53", "UsagePrice": 0.0}, {"CacheNodeType": "cache.r3.2xlarge", "Duration": ********, "FixedPrice": 4132.0, "OfferingType": "Heavy Utilization", "ProductDescription": "redis", "RecurringCharges": [{"RecurringChargeAmount": 0.182, "RecurringChargeFrequency": "Hourly"}], "ReservedCacheNodesOfferingId": "0a516ad8-557f-4310-9dd0-2448c2ff4d62", "UsagePrice": 0.0}, {"CacheNodeType": "cache.c1.xlarge", "Duration": ********, "FixedPrice": 875.0, "OfferingType": "Light Utilization", "ProductDescription": "memcached", "RecurringCharges": [], "ReservedCacheNodesOfferingId": "0b0c1cc5-2177-4150-95d7-c67ec34dcb19", "UsagePrice": 0.363}, {"CacheNodeType": "cache.m4.10xlarge", "Duration": ********, "FixedPrice": 12483.0, "OfferingType": "Heavy Utilization", "ProductDescription": "memcached", "RecurringCharges": [{"RecurringChargeAmount": 0.76, "RecurringChargeFrequency": "Hourly"}], "ReservedCacheNodesOfferingId": "0c2b139b-1cff-43d0-8fba-0c753f9b1950", "UsagePrice": 0.0}, {"CacheNodeType": "cache.c1.xlarge", "Duration": 31536000, "FixedPrice": 1620.0, "OfferingType": "Heavy Utilization", "ProductDescription": "memcached", "RecurringCharges": [{"RecurringChargeAmount": 0.207, "RecurringChargeFrequency": "Hourly"}], "ReservedCacheNodesOfferingId": "0c52115b-38cb-47a2-8dbc-e02e40b6a13f", "UsagePrice": 0.0}, {"CacheNodeType": "cache.m2.4xlarge", "Duration": ********, "FixedPrice": 2381.0, "OfferingType": "Medium Utilization", "ProductDescription": "redis", "RecurringCharges": [], "ReservedCacheNodesOfferingId": "12fcb19c-5416-4e1d-934f-28f1e2cb8599", "UsagePrice": 0.276}, {"CacheNodeType": "cache.m4.xlarge", "Duration": 31536000, "FixedPrice": 616.0, "OfferingType": "Heavy Utilization", "ProductDescription": "memcached", "RecurringCharges": [{"RecurringChargeAmount": 0.112, "RecurringChargeFrequency": "Hourly"}], "ReservedCacheNodesOfferingId": "13af20ad-914d-4d8b-9763-fa2e565f3549", "UsagePrice": 0.0}, {"CacheNodeType": "cache.r3.8xlarge", "Duration": ********, "FixedPrice": 16528.0, "OfferingType": "Heavy Utilization", "ProductDescription": "memcached", "RecurringCharges": [{"RecurringChargeAmount": 0.729, "RecurringChargeFrequency": "Hourly"}], "ReservedCacheNodesOfferingId": "14da3d3f-b526-4dbf-b09b-355578b2a576", "UsagePrice": 0.0}, {"CacheNodeType": "cache.m1.medium", "Duration": ********, "FixedPrice": 140.0, "OfferingType": "Light Utilization", "ProductDescription": "redis", "RecurringCharges": [], "ReservedCacheNodesOfferingId": "15d7018c-71fb-4717-8409-4bdcdca18da7", "UsagePrice": 0.052}, {"CacheNodeType": "cache.m4.4xlarge", "Duration": ********, "FixedPrice": 4993.0, "OfferingType": "Heavy Utilization", "ProductDescription": "memcached", "RecurringCharges": [{"RecurringChargeAmount": 0.304, "RecurringChargeFrequency": "Hourly"}], "ReservedCacheNodesOfferingId": "1ae7ec5f-a76e-49b6-822b-629b1768a13a", "UsagePrice": 0.0}, {"CacheNodeType": "cache.m3.2xlarge", "Duration": 31536000, "FixedPrice": 1772.0, "OfferingType": "Heavy Utilization", "ProductDescription": "redis", "RecurringCharges": [{"RecurringChargeAmount": 0.25, "RecurringChargeFrequency": "Hourly"}], "ReservedCacheNodesOfferingId": "1d31242b-3925-48d1-b882-ce03204e6013", "UsagePrice": 0.0}, {"CacheNodeType": "cache.t1.micro", "Duration": 31536000, "FixedPrice": 54.0, "OfferingType": "Medium Utilization", "ProductDescription": "memcached", "RecurringCharges": [], "ReservedCacheNodesOfferingId": "1ef01f5b-94ff-433f-a530-61a56bfc8e7a", "UsagePrice": 0.008}]}, "comments": {"input": {}, "output": {}}, "description": "Lists available reserved cache node offerings.", "id": "describereseredcachenodeofferings-1481742869998", "title": "DescribeReseredCacheNodeOfferings"}, {"input": {"CacheNodeType": "cache.r3.large", "Duration": "3", "MaxRecords": 25, "OfferingType": "Light Utilization", "ReservedCacheNodesOfferingId": ""}, "output": {"Marker": "", "ReservedCacheNodesOfferings": []}, "comments": {"input": {}, "output": {}}, "description": "Lists available reserved cache node offerings for cache.r3.large nodes with a 3 year commitment.", "id": "describereseredcachenodeofferings-1481742869998", "title": "DescribeReseredCacheNodeOfferings"}, {"input": {"CacheNodeType": "", "Duration": "", "Marker": "", "MaxRecords": 25, "OfferingType": "", "ProductDescription": "", "ReservedCacheNodesOfferingId": "438012d3-4052-4cc7-b2e3-8d3372e0e706"}, "output": {"Marker": "", "ReservedCacheNodesOfferings": []}, "comments": {"input": {}, "output": {}}, "description": "Lists available reserved cache node offerings.", "id": "describereseredcachenodeofferings-1481742869998", "title": "DescribeReseredCacheNodeOfferings"}], "DescribeSnapshots": [{"input": {"SnapshotName": "snapshot-20161212"}, "output": {"Marker": "", "Snapshots": [{"AutoMinorVersionUpgrade": true, "CacheClusterCreateTime": "2016-12-21T22:27:12.543Z", "CacheClusterId": "my-redis5", "CacheNodeType": "cache.m3.large", "CacheParameterGroupName": "default.redis3.2", "CacheSubnetGroupName": "default", "Engine": "redis", "EngineVersion": "3.2.4", "NodeSnapshots": [{"CacheNodeCreateTime": "2016-12-21T22:27:12.543Z", "CacheNodeId": "0001", "CacheSize": "3 MB", "SnapshotCreateTime": "2016-12-21T22:30:26Z"}], "NumCacheNodes": 1, "Port": 6379, "PreferredAvailabilityZone": "us-east-1c", "PreferredMaintenanceWindow": "fri:05:30-fri:06:30", "SnapshotName": "snapshot-20161212", "SnapshotRetentionLimit": 7, "SnapshotSource": "manual", "SnapshotStatus": "available", "SnapshotWindow": "10:00-11:00", "VpcId": "vpc-91280df6"}]}, "comments": {"input": {}, "output": {}}, "description": "Returns information about the snapshot mysnapshot. By default.", "id": "describesnapshots-1481743399584", "title": "DescribeSnapshots"}], "ListAllowedNodeTypeModifications": [{"input": {"ReplicationGroupId": "myreplgroup"}, "output": {"ScaleUpModifications": ["cache.m4.10xlarge", "cache.m4.2xlarge", "cache.m4.4xlarge", "cache.m4.xlarge", "cache.r3.2xlarge", "cache.r3.4xlarge", "cache.r3.8xlarge", "cache.r3.xlarge"]}, "comments": {"input": {}, "output": {}}, "description": "Lists all available node types that you can scale your Redis cluster's or replication group's current node type up to.", "id": "listallowednodetypemodifications-*************", "title": "ListAllowedNodeTypeModifications"}, {"input": {"CacheClusterId": "mycluster"}, "output": {"ScaleUpModifications": []}, "comments": {"input": {}, "output": {}}, "description": "Lists all available node types that you can scale your Redis cluster's or replication group's current node type up to.", "id": "listallowednodetypemodifications-*************", "title": "ListAllowedNodeTypeModifications"}], "ListTagsForResource": [{"input": {"ResourceName": "arn:aws:elasticache:us-west-2:<my-account-id>:cluster:mycluster"}, "output": {"TagList": [{"Key": "APIVersion", "Value": "********"}, {"Key": "Service", "Value": "ElastiCache"}]}, "comments": {"input": {}, "output": {}}, "description": "Lists all cost allocation tags currently on the named resource. A cost allocation tag is a key-value pair where the key is case-sensitive and the value is optional. You can use cost allocation tags to categorize and track your AWS costs.", "id": "listtagsforresource-*************", "title": "ListTagsForResource"}], "ModifyCacheCluster": [{"input": {"ApplyImmediately": true, "CacheClusterId": "redis-cluster", "SnapshotRetentionLimit": 14}, "output": {"CacheCluster": {"AutoMinorVersionUpgrade": true, "CacheClusterCreateTime": "2016-12-22T16:27:56.078Z", "CacheClusterId": "redis-cluster", "CacheClusterStatus": "available", "CacheNodeType": "cache.r3.large", "CacheParameterGroup": {"CacheNodeIdsToReboot": [], "CacheParameterGroupName": "default.redis3.2", "ParameterApplyStatus": "in-sync"}, "CacheSecurityGroups": [], "CacheSubnetGroupName": "default", "ClientDownloadLandingPage": "https://console.aws.amazon.com/elasticache/home#client-download:", "Engine": "redis", "EngineVersion": "3.2.4", "NumCacheNodes": 1, "PendingModifiedValues": {}, "PreferredAvailabilityZone": "us-east-1e", "PreferredMaintenanceWindow": "fri:09:00-fri:10:00", "SnapshotRetentionLimit": 14, "SnapshotWindow": "07:00-08:00"}}, "comments": {"input": {}, "output": {}}, "description": "Copies a snapshot to a specified name.", "id": "modifycachecluster-1482962725919", "title": "ModifyCacheCluster"}], "ModifyCacheParameterGroup": [{"input": {"CacheParameterGroupName": "custom-mem1-4", "ParameterNameValues": [{"ParameterName": "binding_protocol", "ParameterValue": "ascii"}, {"ParameterName": "chunk_size", "ParameterValue": "96"}]}, "output": {"CacheParameterGroupName": "custom-mem1-4"}, "comments": {"input": {}, "output": {}}, "description": "Modifies one or more parameter values in the specified parameter group. You cannot modify any default parameter group.", "id": "modifycacheparametergroup-1482966746787", "title": "ModifyCacheParameterGroup"}], "ModifyCacheSubnetGroup": [{"input": {"CacheSubnetGroupName": "my-sn-grp", "SubnetIds": ["subnet-bcde2345"]}, "output": {"CacheSubnetGroup": {"CacheSubnetGroupDescription": "My subnet group.", "CacheSubnetGroupName": "my-sn-grp", "Subnets": [{"SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetIdentifier": "subnet-a1b2c3d4"}, {"SubnetAvailabilityZone": {"Name": "us-east-1e"}, "SubnetIdentifier": "subnet-1a2b3c4d"}, {"SubnetAvailabilityZone": {"Name": "us-east-1e"}, "SubnetIdentifier": "subnet-bcde2345"}, {"SubnetAvailabilityZone": {"Name": "us-east-1c"}, "SubnetIdentifier": "subnet-1234abcd"}, {"SubnetAvailabilityZone": {"Name": "us-east-1b"}, "SubnetIdentifier": "subnet-abcd1234"}], "VpcId": "vpc-91280df6"}}, "comments": {"input": {}, "output": {}}, "description": "Modifies an existing ElastiCache subnet group.", "id": "modifycachesubnetgroup-1483043446226", "title": "ModifyCacheSubnetGroup"}], "ModifyReplicationGroup": [{"input": {"ApplyImmediately": true, "ReplicationGroupDescription": "Modified replication group", "ReplicationGroupId": "my-redis-rg", "SnapshotRetentionLimit": 30, "SnapshottingClusterId": "my-redis-rg-001"}, "output": {"ReplicationGroup": {"AutomaticFailover": "enabled", "Description": "Modified replication group", "MemberClusters": ["my-redis-rg-001", "my-redis-rg-002", "my-redis-rg-003"], "NodeGroups": [{"NodeGroupId": "0001", "NodeGroupMembers": [{"CacheClusterId": "my-redis-rg-001", "CacheNodeId": "0001", "CurrentRole": "primary", "PreferredAvailabilityZone": "us-east-1b", "ReadEndpoint": {"Address": "my-redis-rg-001.abcdef.0001.use1.cache.amazonaws.com", "Port": 6379}}, {"CacheClusterId": "my-redis-rg-002", "CacheNodeId": "0001", "CurrentRole": "replica", "PreferredAvailabilityZone": "us-east-1a", "ReadEndpoint": {"Address": "my-redis-rg-002.abcdef.0001.use1.cache.amazonaws.com", "Port": 6379}}, {"CacheClusterId": "my-redis-rg-003", "CacheNodeId": "0001", "CurrentRole": "replica", "PreferredAvailabilityZone": "us-east-1c", "ReadEndpoint": {"Address": "my-redis-rg-003.abcdef.0001.use1.cache.amazonaws.com", "Port": 6379}}], "PrimaryEndpoint": {"Address": "my-redis-rg.abcdef.ng.0001.use1.cache.amazonaws.com", "Port": 6379}, "Status": "available"}], "PendingModifiedValues": {}, "ReplicationGroupId": "my-redis-rg", "SnapshottingClusterId": "my-redis-rg-002", "Status": "available"}}, "comments": {"input": {}, "output": {}}, "description": "", "id": "modifyreplicationgroup-1483039689581", "title": "ModifyReplicationGroup"}], "PurchaseReservedCacheNodesOffering": [{"input": {"ReservedCacheNodesOfferingId": "1ef01f5b-94ff-433f-a530-61a56bfc8e7a"}, "comments": {"input": {}, "output": {}}, "description": "Allows you to purchase a reserved cache node offering.", "id": "purchasereservedcachenodesofferings-1483040798484", "title": "PurchaseReservedCacheNodesOfferings"}], "RebootCacheCluster": [{"input": {"CacheClusterId": "custom-mem1-4  ", "CacheNodeIdsToReboot": ["0001", "0002"]}, "output": {"CacheCluster": {"AutoMinorVersionUpgrade": true, "CacheClusterCreateTime": "2016-12-21T21:59:43.794Z", "CacheClusterId": "my-mem-cluster", "CacheClusterStatus": "rebooting cache cluster nodes", "CacheNodeType": "cache.t2.medium", "CacheParameterGroup": {"CacheNodeIdsToReboot": [], "CacheParameterGroupName": "default.memcached1.4", "ParameterApplyStatus": "in-sync"}, "CacheSecurityGroups": [], "CacheSubnetGroupName": "default", "ClientDownloadLandingPage": "https://console.aws.amazon.com/elasticache/home#client-download:", "ConfigurationEndpoint": {"Address": "my-mem-cluster.abcdef.cfg.use1.cache.amazonaws.com", "Port": 11211}, "Engine": "memcached", "EngineVersion": "1.4.24", "NumCacheNodes": 2, "PendingModifiedValues": {}, "PreferredAvailabilityZone": "Multiple", "PreferredMaintenanceWindow": "wed:06:00-wed:07:00"}}, "comments": {"input": {}, "output": {}}, "description": "Reboots the specified nodes in the names cluster.", "id": "rebootcachecluster-1482969019505", "title": "RebootCacheCluster"}], "RemoveTagsFromResource": [{"input": {"ResourceName": "arn:aws:elasticache:us-east-1:1234567890:cluster:my-mem-cluster", "TagKeys": ["A", "C", "E"]}, "output": {"TagList": [{"Key": "B", "Value": "Banana"}, {"Key": "D", "Value": "Dog"}, {"Key": "F", "Value": "Fox"}, {"Key": "I", "Value": ""}, {"Key": "K", "Value": "<PERSON>e"}]}, "comments": {"input": {}, "output": {}}, "description": "Removes tags identified by a list of tag keys from the list of tags on the specified resource.", "id": "removetagsfromresource-1483037920947", "title": "RemoveTagsFromResource"}], "ResetCacheParameterGroup": [{"input": {"CacheParameterGroupName": "custom-mem1-4", "ResetAllParameters": true}, "output": {"CacheParameterGroupName": "custom-mem1-4"}, "comments": {"input": {}, "output": {}}, "description": "Modifies the parameters of a cache parameter group to the engine or system default value.", "id": "resetcacheparametergroup-1483038334014", "title": "ResetCacheParameterGroup"}], "RevokeCacheSecurityGroupIngress": [{"input": {"CacheSecurityGroupName": "my-sec-grp", "EC2SecurityGroupName": "my-ec2-sec-grp", "EC2SecurityGroupOwnerId": "1234567890"}, "comments": {"input": {}, "output": {}}, "description": "Returns a list of cache security group descriptions. If a cache security group name is specified, the list contains only the description of that group.", "id": "describecachesecuritygroups-1483047200801", "title": "DescribeCacheSecurityGroups"}]}}