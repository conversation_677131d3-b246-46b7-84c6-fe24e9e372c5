import pandas as pd
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import TruncatedSVD
from scipy.sparse import csr_matrix
import pickle
import logging
from typing import List, Dict, Tuple
import os

logger = logging.getLogger(__name__)

class CollaborativeFilteringEngine:
    def __init__(self):
        self.user_item_matrix = None
        self.item_similarity_matrix = None
        self.user_similarity_matrix = None
        self.svd_model = None
        self.user_factors = None
        self.item_factors = None
        self.users_df = None
        self.products_df = None
        self.interactions_df = None
        self.user_to_idx = {}
        self.product_to_idx = {}
        self.idx_to_user = {}
        self.idx_to_product = {}
        
    def load_data(self, users_csv: str, products_csv: str, interactions_csv: str):
        """Load data from CSV files"""
        try:
            self.users_df = pd.read_csv(users_csv)
            self.products_df = pd.read_csv(products_csv)
            self.interactions_df = pd.read_csv(interactions_csv)
            
            logger.info(f"Loaded {len(self.users_df)} users, {len(self.products_df)} products, {len(self.interactions_df)} interactions")
            
            # Create mappings
            unique_users = self.users_df['user_id'].unique()
            unique_products = self.products_df['product_id'].unique()
            
            self.user_to_idx = {user: idx for idx, user in enumerate(unique_users)}
            self.product_to_idx = {product: idx for idx, product in enumerate(unique_products)}
            self.idx_to_user = {idx: user for user, idx in self.user_to_idx.items()}
            self.idx_to_product = {idx: product for product, idx in self.product_to_idx.items()}
            
            return True
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            return False
    
    def preprocess_interactions(self):
        """Convert interactions to user-item matrix"""
        try:
            # Filter interactions to only include users and products in our datasets
            valid_interactions = self.interactions_df[
                (self.interactions_df['user_id'].isin(self.users_df['user_id'])) &
                (self.interactions_df['product_id'].isin(self.products_df['product_id']))
            ]
            
            # Create implicit scores (convert different event types to scores)
            implicit_scores = valid_interactions.copy()
            
            # Aggregate multiple interactions per user-item pair
            user_item_scores = implicit_scores.groupby(['user_id', 'product_id'])['value'].sum().reset_index()
            
            # Create user-item matrix
            n_users = len(self.user_to_idx)
            n_items = len(self.product_to_idx)
            
            user_item_matrix = np.zeros((n_users, n_items))
            
            for _, row in user_item_scores.iterrows():
                user_idx = self.user_to_idx.get(row['user_id'])
                item_idx = self.product_to_idx.get(row['product_id'])
                
                if user_idx is not None and item_idx is not None:
                    user_item_matrix[user_idx, item_idx] = row['value']
            
            self.user_item_matrix = user_item_matrix
            logger.info(f"Created user-item matrix of shape {user_item_matrix.shape}")
            
            return True
        except Exception as e:
            logger.error(f"Error preprocessing interactions: {e}")
            return False
    
    def train_item_based_cf(self):
        """Train item-based collaborative filtering model"""
        try:
            if self.user_item_matrix is None:
                raise ValueError("User-item matrix not created. Run preprocess_interactions first.")
            
            # Transpose to get item-user matrix for item-based CF
            item_user_matrix = self.user_item_matrix.T
            
            # Calculate item-item similarity using cosine similarity
            # Only calculate for items that have at least one interaction
            item_norms = np.linalg.norm(item_user_matrix, axis=1)
            non_zero_items = item_norms > 0
            
            if np.sum(non_zero_items) == 0:
                raise ValueError("No items have interactions")
            
            # Calculate similarity only for non-zero items
            filtered_matrix = item_user_matrix[non_zero_items]
            similarity_matrix = cosine_similarity(filtered_matrix)
            
            # Create full similarity matrix
            n_items = len(self.product_to_idx)
            full_similarity = np.zeros((n_items, n_items))
            
            non_zero_indices = np.where(non_zero_items)[0]
            for i, idx_i in enumerate(non_zero_indices):
                for j, idx_j in enumerate(non_zero_indices):
                    full_similarity[idx_i, idx_j] = similarity_matrix[i, j]
            
            self.item_similarity_matrix = full_similarity
            logger.info("Item-based collaborative filtering model trained successfully")
            
            return True
        except Exception as e:
            logger.error(f"Error training item-based CF: {e}")
            return False
    
    def train_svd_model(self, n_components=50):
        """Train SVD matrix factorization model"""
        try:
            if self.user_item_matrix is None:
                raise ValueError("User-item matrix not created. Run preprocess_interactions first.")
            
            # Convert to sparse matrix for efficiency
            sparse_matrix = csr_matrix(self.user_item_matrix)
            
            # Apply SVD
            self.svd_model = TruncatedSVD(n_components=n_components, random_state=42)
            user_factors = self.svd_model.fit_transform(sparse_matrix)
            item_factors = self.svd_model.components_.T
            
            self.user_factors = user_factors
            self.item_factors = item_factors
            
            logger.info(f"SVD model trained with {n_components} components")
            return True
        except Exception as e:
            logger.error(f"Error training SVD model: {e}")
            return False
    
    def get_item_based_recommendations(self, user_id: str, n_recommendations: int = 10) -> List[Dict]:
        """Get recommendations using item-based collaborative filtering"""
        try:
            if user_id not in self.user_to_idx:
                logger.warning(f"User {user_id} not found")
                return self._get_popular_items(n_recommendations)
            
            user_idx = self.user_to_idx[user_id]
            user_interactions = self.user_item_matrix[user_idx]
            
            # Find items the user has interacted with
            interacted_items = np.where(user_interactions > 0)[0]
            
            if len(interacted_items) == 0:
                logger.info(f"User {user_id} has no interactions, returning popular items")
                return self._get_popular_items(n_recommendations)
            
            # Calculate recommendation scores
            recommendation_scores = np.zeros(len(self.product_to_idx))
            
            for item_idx in interacted_items:
                # Get similar items
                similar_items = self.item_similarity_matrix[item_idx]
                
                # Weight by user's interaction strength
                user_interaction_strength = user_interactions[item_idx]
                recommendation_scores += similar_items * user_interaction_strength
            
            # Remove already interacted items
            recommendation_scores[interacted_items] = 0
            
            # Get top recommendations
            top_item_indices = np.argsort(recommendation_scores)[-n_recommendations:][::-1]
            
            recommendations = []
            for item_idx in top_item_indices:
                if recommendation_scores[item_idx] > 0:
                    product_id = self.idx_to_product[item_idx]
                    product_info = self.products_df[self.products_df['product_id'] == product_id].iloc[0]
                    
                    recommendations.append({
                        'product_id': product_id,
                        'title': product_info['title'],
                        'category': product_info['category'],
                        'brand': product_info['brand'],
                        'price': product_info['price'],
                        'score': float(recommendation_scores[item_idx])
                    })
            
            return recommendations
        except Exception as e:
            logger.error(f"Error getting item-based recommendations: {e}")
            return self._get_popular_items(n_recommendations)
    
    def get_svd_recommendations(self, user_id: str, n_recommendations: int = 10) -> List[Dict]:
        """Get recommendations using SVD matrix factorization"""
        try:
            if self.svd_model is None:
                raise ValueError("SVD model not trained")
            
            if user_id not in self.user_to_idx:
                logger.warning(f"User {user_id} not found")
                return self._get_popular_items(n_recommendations)
            
            user_idx = self.user_to_idx[user_id]
            
            # Get user factors
            user_vector = self.user_factors[user_idx]
            
            # Calculate scores for all items
            scores = np.dot(user_vector, self.item_factors.T)
            
            # Remove already interacted items
            user_interactions = self.user_item_matrix[user_idx]
            interacted_items = np.where(user_interactions > 0)[0]
            scores[interacted_items] = -np.inf
            
            # Get top recommendations
            top_item_indices = np.argsort(scores)[-n_recommendations:][::-1]
            
            recommendations = []
            for item_idx in top_item_indices:
                if scores[item_idx] > -np.inf:
                    product_id = self.idx_to_product[item_idx]
                    product_info = self.products_df[self.products_df['product_id'] == product_id].iloc[0]
                    
                    recommendations.append({
                        'product_id': product_id,
                        'title': product_info['title'],
                        'category': product_info['category'],
                        'brand': product_info['brand'],
                        'price': product_info['price'],
                        'score': float(scores[item_idx])
                    })
            
            return recommendations
        except Exception as e:
            logger.error(f"Error getting SVD recommendations: {e}")
            return self._get_popular_items(n_recommendations)
    
    def _get_popular_items(self, n_recommendations: int = 10) -> List[Dict]:
        """Get popular items as fallback recommendations"""
        try:
            # Calculate popularity based on interaction frequency
            product_popularity = self.interactions_df['product_id'].value_counts()
            popular_products = product_popularity.head(n_recommendations).index.tolist()
            
            recommendations = []
            for product_id in popular_products:
                if product_id in self.products_df['product_id'].values:
                    product_info = self.products_df[self.products_df['product_id'] == product_id].iloc[0]
                    
                    recommendations.append({
                        'product_id': product_id,
                        'title': product_info['title'],
                        'category': product_info['category'],
                        'brand': product_info['brand'],
                        'price': product_info['price'],
                        'score': float(product_popularity[product_id])
                    })
            
            return recommendations
        except Exception as e:
            logger.error(f"Error getting popular items: {e}")
            return []
    
    def get_analytics_data(self) -> Dict:
        """Get analytics data for dashboard"""
        try:
            analytics = {}
            
            # Category distribution
            category_counts = self.products_df['category'].value_counts().to_dict()
            analytics['category_distribution'] = category_counts
            
            # Popular products
            product_popularity = self.interactions_df['product_id'].value_counts().head(10)
            popular_products = []
            for product_id, count in product_popularity.items():
                if product_id in self.products_df['product_id'].values:
                    product_info = self.products_df[self.products_df['product_id'] == product_id].iloc[0]
                    popular_products.append({
                        'product_id': product_id,
                        'title': product_info['title'],
                        'interactions': int(count)
                    })
            analytics['popular_products'] = popular_products
            
            # User activity over time
            self.interactions_df['timestamp'] = pd.to_datetime(self.interactions_df['timestamp'])
            daily_activity = self.interactions_df.groupby(self.interactions_df['timestamp'].dt.date).size()
            analytics['daily_activity'] = {str(date): int(count) for date, count in daily_activity.items()}
            
            # Basic stats
            analytics['total_users'] = len(self.users_df)
            analytics['total_products'] = len(self.products_df)
            analytics['total_interactions'] = len(self.interactions_df)
            
            return analytics
        except Exception as e:
            logger.error(f"Error getting analytics data: {e}")
            return {}
    
    def save_model(self, filepath: str):
        """Save trained model to file"""
        try:
            model_data = {
                'user_item_matrix': self.user_item_matrix,
                'item_similarity_matrix': self.item_similarity_matrix,
                'svd_model': self.svd_model,
                'user_factors': self.user_factors,
                'item_factors': self.item_factors,
                'user_to_idx': self.user_to_idx,
                'product_to_idx': self.product_to_idx,
                'idx_to_user': self.idx_to_user,
                'idx_to_product': self.idx_to_product
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            
            logger.info(f"Model saved to {filepath}")
            return True
        except Exception as e:
            logger.error(f"Error saving model: {e}")
            return False
    
    def load_model(self, filepath: str):
        """Load trained model from file"""
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.user_item_matrix = model_data['user_item_matrix']
            self.item_similarity_matrix = model_data['item_similarity_matrix']
            self.svd_model = model_data['svd_model']
            self.user_factors = model_data['user_factors']
            self.item_factors = model_data['item_factors']
            self.user_to_idx = model_data['user_to_idx']
            self.product_to_idx = model_data['product_to_idx']
            self.idx_to_user = model_data['idx_to_user']
            self.idx_to_product = model_data['idx_to_product']
            
            logger.info(f"Model loaded from {filepath}")
            return True
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            return False