[{"C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\Login.js": "3", "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\Dashboard.js": "4", "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\Navbar.js": "5", "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\UserProfile.js": "6", "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\ProductCatalog.js": "7", "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\ui\\sonner.jsx": "8", "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\Chart.js": "9", "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\ui\\label.jsx": "10", "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\ui\\input.jsx": "11", "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\ui\\button.jsx": "12", "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\ui\\card.jsx": "13", "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\ui\\tabs.jsx": "14", "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\lib\\utils.js": "15"}, {"size": 266, "mtime": 1757182146657, "results": "16", "hashOfConfig": "17"}, {"size": 3494, "mtime": 1757182145123, "results": "18", "hashOfConfig": "17"}, {"size": 10276, "mtime": 1757182145169, "results": "19", "hashOfConfig": "17"}, {"size": 12521, "mtime": 1757182145140, "results": "20", "hashOfConfig": "17"}, {"size": 4097, "mtime": 1757182145184, "results": "21", "hashOfConfig": "17"}, {"size": 11607, "mtime": 1757182145220, "results": "22", "hashOfConfig": "17"}, {"size": 11040, "mtime": 1757182410132, "results": "23", "hashOfConfig": "17"}, {"size": 811, "mtime": 1757182146099, "results": "24", "hashOfConfig": "17"}, {"size": 6443, "mtime": 1757182145132, "results": "25", "hashOfConfig": "17"}, {"size": 545, "mtime": 1757182145724, "results": "26", "hashOfConfig": "17"}, {"size": 711, "mtime": 1757182145708, "results": "27", "hashOfConfig": "17"}, {"size": 1714, "mtime": 1757182145382, "results": "28", "hashOfConfig": "17"}, {"size": 1494, "mtime": 1757182145420, "results": "29", "hashOfConfig": "17"}, {"size": 1574, "mtime": 1757182146143, "results": "30", "hashOfConfig": "17"}, {"size": 143, "mtime": 1757182146674, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "87q0a4", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\Login.js", [], [], "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\UserProfile.js", [], [], "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\ProductCatalog.js", [], [], "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\ui\\sonner.jsx", [], [], "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\Chart.js", [], [], "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\ui\\label.jsx", [], [], "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\ui\\input.jsx", [], [], "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\ui\\button.jsx", [], [], "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\ui\\card.jsx", [], [], "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\components\\ui\\tabs.jsx", [], [], "C:\\Users\\<USER>\\Products-recommendation-system\\Products-Recommendation-System\\frontend\\src\\lib\\utils.js", [], []]