{"pagination": {"GetApiKeys": {"input_token": "position", "output_token": "position", "limit_key": "limit", "result_key": "items"}, "GetBasePathMappings": {"input_token": "position", "output_token": "position", "limit_key": "limit", "result_key": "items"}, "GetClientCertificates": {"input_token": "position", "output_token": "position", "limit_key": "limit", "result_key": "items"}, "GetDeployments": {"input_token": "position", "output_token": "position", "limit_key": "limit", "result_key": "items"}, "GetDomainNames": {"input_token": "position", "output_token": "position", "limit_key": "limit", "result_key": "items"}, "GetModels": {"input_token": "position", "output_token": "position", "limit_key": "limit", "result_key": "items"}, "GetResources": {"input_token": "position", "output_token": "position", "limit_key": "limit", "result_key": "items"}, "GetRestApis": {"input_token": "position", "output_token": "position", "limit_key": "limit", "result_key": "items"}, "GetUsage": {"input_token": "position", "output_token": "position", "limit_key": "limit", "result_key": "items", "non_aggregate_keys": ["usagePlanId", "startDate", "endDate"]}, "GetUsagePlans": {"input_token": "position", "output_token": "position", "limit_key": "limit", "result_key": "items"}, "GetUsagePlanKeys": {"input_token": "position", "output_token": "position", "limit_key": "limit", "result_key": "items"}, "GetVpcLinks": {"input_token": "position", "limit_key": "limit", "output_token": "position", "result_key": "items"}, "GetAuthorizers": {"input_token": "position", "limit_key": "limit", "output_token": "position", "result_key": "items"}, "GetDocumentationParts": {"input_token": "position", "limit_key": "limit", "output_token": "position", "result_key": "items"}, "GetDocumentationVersions": {"input_token": "position", "limit_key": "limit", "output_token": "position", "result_key": "items"}, "GetGatewayResponses": {"input_token": "position", "limit_key": "limit", "output_token": "position", "result_key": "items"}, "GetRequestValidators": {"input_token": "position", "limit_key": "limit", "output_token": "position", "result_key": "items"}, "GetSdkTypes": {"input_token": "position", "limit_key": "limit", "output_token": "position", "result_key": "items"}}}