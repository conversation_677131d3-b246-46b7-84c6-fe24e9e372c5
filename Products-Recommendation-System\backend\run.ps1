param(
  [string]$Port = "8000"
)

# Always run from this script's directory (backend)
Set-Location $PSScriptRoot

Write-Host "[Backend] Working directory: $PSScriptRoot"

# 1) Ensure Python venv
if (-not (Test-Path ".venv")) {
  Write-Host "[Backend] Creating virtual environment..."
  python -m venv .venv
}

$venvPython = Join-Path $PSScriptRoot ".venv\Scripts\python.exe"
$venvPip = Join-Path $PSScriptRoot ".venv\Scripts\pip.exe"

# 2) Install dependencies
Write-Host "[Backend] Upgrading pip and installing requirements..."
& $venvPip install --upgrade pip
& $venvPip install -r requirements.txt

# 3) Show environment info
Write-Host "[Backend] Using Python: $venvPython"
Write-Host "[Backend] Starting FastAPI (Uvicorn) on port $Port..."

# 4) Start server
& $venvPython -m uvicorn server:app --host 0.0.0.0 --port $Port --reload