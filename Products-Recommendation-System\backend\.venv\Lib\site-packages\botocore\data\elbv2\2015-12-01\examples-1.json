{"version": "1.0", "examples": {"AddTags": [{"input": {"ResourceArns": ["arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188"], "Tags": [{"Key": "project", "Value": "lima"}, {"Key": "department", "Value": "digital-media"}]}, "comments": {"input": {}, "output": {}}, "description": "This example adds the specified tags to the specified load balancer.", "id": "elbv2-add-tags-1", "title": "To add tags to a load balancer"}], "CreateListener": [{"input": {"DefaultActions": [{"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067", "Type": "forward"}], "LoadBalancerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188", "Port": 80, "Protocol": "HTTP"}, "output": {"Listeners": [{"DefaultActions": [{"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067", "Type": "forward"}], "ListenerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:listener/app/my-load-balancer/50dc6c495c0c9188/f2f7dc8efc522ab2", "LoadBalancerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188", "Port": 80, "Protocol": "HTTP"}]}, "comments": {"input": {}, "output": {}}, "description": "This example creates an HTTP listener for the specified load balancer that forwards requests to the specified target group.", "id": "elbv2-create-listener-1", "title": "To create an HTTP listener"}, {"input": {"Certificates": [{"CertificateArn": "arn:aws:iam::123456789012:server-certificate/my-server-cert"}], "DefaultActions": [{"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067", "Type": "forward"}], "LoadBalancerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188", "Port": 443, "Protocol": "HTTPS", "SslPolicy": "ELBSecurityPolicy-2015-05"}, "output": {"Listeners": [{"Certificates": [{"CertificateArn": "arn:aws:iam::123456789012:server-certificate/my-server-cert"}], "DefaultActions": [{"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067", "Type": "forward"}], "ListenerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:listener/app/my-load-balancer/50dc6c495c0c9188/f2f7dc8efc522ab2", "LoadBalancerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188", "Port": 443, "Protocol": "HTTPS", "SslPolicy": "ELBSecurityPolicy-2015-05"}]}, "comments": {"input": {}, "output": {}}, "description": "This example creates an HTTPS listener for the specified load balancer that forwards requests to the specified target group. Note that you must specify an SSL certificate for an HTTPS listener. You can create and manage certificates using AWS Certificate Manager (ACM). Alternatively, you can create a certificate using SSL/TLS tools, get the certificate signed by a certificate authority (CA), and upload the certificate to AWS Identity and Access Management (IAM).", "id": "elbv2-create-listener-2", "title": "To create an HTTPS listener"}], "CreateLoadBalancer": [{"input": {"Name": "my-load-balancer", "Subnets": ["subnet-b7d581c0", "subnet-8360a9e7"]}, "output": {"LoadBalancers": [{"AvailabilityZones": [{"SubnetId": "subnet-8360a9e7", "ZoneName": "us-west-2a"}, {"SubnetId": "subnet-b7d581c0", "ZoneName": "us-west-2b"}], "CanonicalHostedZoneId": "Z2P70J7EXAMPLE", "CreatedTime": "2016-03-25T21:26:12.920Z", "DNSName": "my-load-balancer-424835706.us-west-2.elb.amazonaws.com", "LoadBalancerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188", "LoadBalancerName": "my-load-balancer", "Scheme": "internet-facing", "SecurityGroups": ["sg-5943793c"], "State": {"Code": "provisioning"}, "Type": "application", "VpcId": "vpc-3ac0fb5f"}]}, "comments": {"input": {}, "output": {}}, "description": "This example creates an Internet-facing load balancer and enables the Availability Zones for the specified subnets.", "id": "elbv2-create-load-balancer-1", "title": "To create an Internet-facing load balancer"}, {"input": {"Name": "my-internal-load-balancer", "Scheme": "internal", "SecurityGroups": [], "Subnets": ["subnet-b7d581c0", "subnet-8360a9e7"]}, "output": {"LoadBalancers": [{"AvailabilityZones": [{"SubnetId": "subnet-8360a9e7", "ZoneName": "us-west-2a"}, {"SubnetId": "subnet-b7d581c0", "ZoneName": "us-west-2b"}], "CanonicalHostedZoneId": "Z2P70J7EXAMPLE", "CreatedTime": "2016-03-25T21:29:48.850Z", "DNSName": "internal-my-internal-load-balancer-1529930873.us-west-2.elb.amazonaws.com", "LoadBalancerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-internal-load-balancer/5b49b8d4303115c2", "LoadBalancerName": "my-internal-load-balancer", "Scheme": "internal", "SecurityGroups": ["sg-5943793c"], "State": {"Code": "provisioning"}, "Type": "application", "VpcId": "vpc-3ac0fb5f"}]}, "comments": {"input": {}, "output": {}}, "description": "This example creates an internal load balancer and enables the Availability Zones for the specified subnets.", "id": "elbv2-create-load-balancer-2", "title": "To create an internal load balancer"}], "CreateRule": [{"input": {"Actions": [{"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067", "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "Values": ["/img/*"]}], "ListenerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:listener/app/my-load-balancer/50dc6c495c0c9188/f2f7dc8efc522ab2", "Priority": 10}, "output": {"Rules": [{"Actions": [{"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067", "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "Values": ["/img/*"]}], "IsDefault": false, "Priority": "10", "RuleArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:listener-rule/app/my-load-balancer/50dc6c495c0c9188/f2f7dc8efc522ab2/9683b2d02a6cabee"}]}, "comments": {"input": {}, "output": {}}, "description": "This example creates a rule that forwards requests to the specified target group if the URL contains the specified pattern (for example, /img/*).", "id": "elbv2-create-rule-1", "title": "To create a rule"}], "CreateTargetGroup": [{"input": {"Name": "my-targets", "Port": 80, "Protocol": "HTTP", "VpcId": "vpc-3ac0fb5f"}, "output": {"TargetGroups": [{"HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/", "HealthCheckPort": "traffic-port", "HealthCheckProtocol": "HTTP", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 5, "Matcher": {"HttpCode": "200"}, "Port": 80, "Protocol": "HTTP", "TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067", "TargetGroupName": "my-targets", "UnhealthyThresholdCount": 2, "VpcId": "vpc-3ac0fb5f"}]}, "comments": {"input": {}, "output": {}}, "description": "This example creates a target group that you can use to route traffic to targets using HTTP on port 80. This target group uses the default health check configuration.", "id": "elbv2-create-target-group-1", "title": "To create a target group"}], "DeleteListener": [{"input": {"ListenerArn": "arn:aws:elasticloadbalancing:ua-west-2:123456789012:listener/app/my-load-balancer/50dc6c495c0c9188/f2f7dc8efc522ab2"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified listener.", "id": "elbv2-delete-listener-1", "title": "To delete a listener"}], "DeleteLoadBalancer": [{"input": {"LoadBalancerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified load balancer.", "id": "elbv2-delete-load-balancer-1", "title": "To delete a load balancer"}], "DeleteRule": [{"input": {"RuleArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:listener-rule/app/my-load-balancer/50dc6c495c0c9188/f2f7dc8efc522ab2/1291d13826f405c3"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified rule.", "id": "elbv2-delete-rule-1", "title": "To delete a rule"}], "DeleteTargetGroup": [{"input": {"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified target group.", "id": "elbv2-delete-target-group-1", "title": "To delete a target group"}], "DeregisterTargets": [{"input": {"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067", "Targets": [{"Id": "i-0f76fade"}]}, "comments": {"input": {}, "output": {}}, "description": "This example deregisters the specified instance from the specified target group.", "id": "elbv2-deregister-targets-1", "title": "To deregister a target from a target group"}], "DescribeListeners": [{"input": {"ListenerArns": ["arn:aws:elasticloadbalancing:us-west-2:123456789012:listener/app/my-load-balancer/50dc6c495c0c9188/f2f7dc8efc522ab2"]}, "output": {"Listeners": [{"DefaultActions": [{"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067", "Type": "forward"}], "ListenerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:listener/app/my-load-balancer/50dc6c495c0c9188/f2f7dc8efc522ab2", "LoadBalancerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188", "Port": 80, "Protocol": "HTTP"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified listener.", "id": "elbv2-describe-listeners-1", "title": "To describe a listener"}], "DescribeLoadBalancerAttributes": [{"input": {"LoadBalancerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188"}, "output": {"Attributes": [{"Key": "access_logs.s3.enabled", "Value": "false"}, {"Key": "idle_timeout.timeout_seconds", "Value": "60"}, {"Key": "access_logs.s3.prefix", "Value": ""}, {"Key": "deletion_protection.enabled", "Value": "false"}, {"Key": "access_logs.s3.bucket", "Value": ""}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the attributes of the specified load balancer.", "id": "elbv2-describe-load-balancer-attributes-1", "title": "To describe load balancer attributes"}], "DescribeLoadBalancers": [{"input": {"LoadBalancerArns": ["arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188"]}, "output": {"LoadBalancers": [{"AvailabilityZones": [{"SubnetId": "subnet-8360a9e7", "ZoneName": "us-west-2a"}, {"SubnetId": "subnet-b7d581c0", "ZoneName": "us-west-2b"}], "CanonicalHostedZoneId": "Z2P70J7EXAMPLE", "CreatedTime": "2016-03-25T21:26:12.920Z", "DNSName": "my-load-balancer-424835706.us-west-2.elb.amazonaws.com", "LoadBalancerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188", "LoadBalancerName": "my-load-balancer", "Scheme": "internet-facing", "SecurityGroups": ["sg-5943793c"], "State": {"Code": "active"}, "Type": "application", "VpcId": "vpc-3ac0fb5f"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified load balancer.", "id": "elbv2-describe-load-balancers-1", "title": "To describe a load balancer"}], "DescribeRules": [{"input": {"RuleArns": ["arn:aws:elasticloadbalancing:us-west-2:123456789012:listener-rule/app/my-load-balancer/50dc6c495c0c9188/f2f7dc8efc522ab2/9683b2d02a6cabee"]}, "output": {"Rules": [{"Actions": [{"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067", "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "Values": ["/img/*"]}], "IsDefault": false, "Priority": "10", "RuleArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:listener-rule/app/my-load-balancer/50dc6c495c0c9188/f2f7dc8efc522ab2/9683b2d02a6cabee"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified rule.", "id": "elbv2-describe-rules-1", "title": "To describe a rule"}], "DescribeSSLPolicies": [{"input": {"Names": ["ELBSecurityPolicy-2015-05"]}, "output": {"SslPolicies": [{"Ciphers": [{"Name": "ECDHE-ECDSA-AES128-GCM-SHA256", "Priority": 1}, {"Name": "ECDHE-RSA-AES128-GCM-SHA256", "Priority": 2}, {"Name": "ECDHE-ECDSA-AES128-SHA256", "Priority": 3}, {"Name": "ECDHE-RSA-AES128-SHA256", "Priority": 4}, {"Name": "ECDHE-ECDSA-AES128-SHA", "Priority": 5}, {"Name": "ECDHE-RSA-AES128-SHA", "Priority": 6}, {"Name": "DHE-RSA-AES128-SHA", "Priority": 7}, {"Name": "ECDHE-ECDSA-AES256-GCM-SHA384", "Priority": 8}, {"Name": "ECDHE-RSA-AES256-GCM-SHA384", "Priority": 9}, {"Name": "ECDHE-ECDSA-AES256-SHA384", "Priority": 10}, {"Name": "ECDHE-RSA-AES256-SHA384", "Priority": 11}, {"Name": "ECDHE-RSA-AES256-SHA", "Priority": 12}, {"Name": "ECDHE-ECDSA-AES256-SHA", "Priority": 13}, {"Name": "AES128-GCM-SHA256", "Priority": 14}, {"Name": "AES128-SHA256", "Priority": 15}, {"Name": "AES128-SHA", "Priority": 16}, {"Name": "AES256-GCM-SHA384", "Priority": 17}, {"Name": "AES256-SHA256", "Priority": 18}, {"Name": "AES256-SHA", "Priority": 19}], "Name": "ELBSecurityPolicy-2015-05", "SslProtocols": ["TLSv1", "TLSv1.1", "TLSv1.2"]}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified policy used for SSL negotiation.", "id": "elbv2-describe-ssl-policies-1", "title": "To describe a policy used for SSL negotiation"}], "DescribeTags": [{"input": {"ResourceArns": ["arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188"]}, "output": {"TagDescriptions": [{"ResourceArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188", "Tags": [{"Key": "project", "Value": "lima"}, {"Key": "department", "Value": "digital-media"}]}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the tags assigned to the specified load balancer.", "id": "elbv2-describe-tags-1", "title": "To describe the tags assigned to a load balancer"}], "DescribeTargetGroupAttributes": [{"input": {"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067"}, "output": {"Attributes": [{"Key": "stickiness.enabled", "Value": "false"}, {"Key": "deregistration_delay.timeout_seconds", "Value": "300"}, {"Key": "stickiness.type", "Value": "lb_cookie"}, {"Key": "stickiness.lb_cookie.duration_seconds", "Value": "86400"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the attributes of the specified target group.", "id": "elbv2-describe-target-group-attributes-1", "title": "To describe target group attributes"}], "DescribeTargetGroups": [{"input": {"TargetGroupArns": ["arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067"]}, "output": {"TargetGroups": [{"HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/", "HealthCheckPort": "traffic-port", "HealthCheckProtocol": "HTTP", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 5, "LoadBalancerArns": ["arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188"], "Matcher": {"HttpCode": "200"}, "Port": 80, "Protocol": "HTTP", "TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067", "TargetGroupName": "my-targets", "UnhealthyThresholdCount": 2, "VpcId": "vpc-3ac0fb5f"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified target group.", "id": "elbv2-describe-target-groups-1", "title": "To describe a target group"}], "DescribeTargetHealth": [{"input": {"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067"}, "output": {"TargetHealthDescriptions": [{"Target": {"Id": "i-0f76fade", "Port": 80}, "TargetHealth": {"Description": "Given target group is not configured to receive traffic from ELB", "Reason": "Target.NotInUse", "State": "unused"}}, {"HealthCheckPort": "80", "Target": {"Id": "i-0f76fade", "Port": 80}, "TargetHealth": {"State": "healthy"}}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the health of the targets for the specified target group. One target is healthy but the other is not specified in an action, so it can't receive traffic from the load balancer.", "id": "elbv2-describe-target-health-1", "title": "To describe the health of the targets for a target group"}, {"input": {"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067", "Targets": [{"Id": "i-0f76fade", "Port": 80}]}, "output": {"TargetHealthDescriptions": [{"HealthCheckPort": "80", "Target": {"Id": "i-0f76fade", "Port": 80}, "TargetHealth": {"State": "healthy"}}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the health of the specified target. This target is healthy.", "id": "elbv2-describe-target-health-2", "title": "To describe the health of a target"}], "ModifyListener": [{"input": {"DefaultActions": [{"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-new-targets/2453ed029918f21f", "Type": "forward"}], "ListenerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:listener/app/my-load-balancer/50dc6c495c0c9188/f2f7dc8efc522ab2"}, "output": {"Listeners": [{"DefaultActions": [{"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-new-targets/2453ed029918f21f", "Type": "forward"}], "ListenerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:listener/app/my-load-balancer/50dc6c495c0c9188/f2f7dc8efc522ab2", "LoadBalancerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188", "Port": 80, "Protocol": "HTTP"}]}, "comments": {"input": {}, "output": {}}, "description": "This example changes the default action for the specified listener.", "id": "elbv2-modify-listener-1", "title": "To change the default action for a listener"}, {"input": {"Certificates": [{"CertificateArn": "arn:aws:iam::123456789012:server-certificate/my-new-server-cert"}], "ListenerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:listener/app/my-load-balancer/50dc6c495c0c9188/0467ef3c8400ae65"}, "output": {"Listeners": [{"Certificates": [{"CertificateArn": "arn:aws:iam::123456789012:server-certificate/my-new-server-cert"}], "DefaultActions": [{"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067", "Type": "forward"}], "ListenerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:listener/app/my-load-balancer/50dc6c495c0c9188/0467ef3c8400ae65", "LoadBalancerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188", "Port": 443, "Protocol": "HTTPS", "SslPolicy": "ELBSecurityPolicy-2015-05"}]}, "comments": {"input": {}, "output": {}}, "description": "This example changes the server certificate for the specified HTTPS listener.", "id": "elbv2-modify-listener-2", "title": "To change the server certificate"}], "ModifyLoadBalancerAttributes": [{"input": {"Attributes": [{"Key": "deletion_protection.enabled", "Value": "true"}], "LoadBalancerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188"}, "output": {"Attributes": [{"Key": "deletion_protection.enabled", "Value": "true"}, {"Key": "access_logs.s3.enabled", "Value": "false"}, {"Key": "idle_timeout.timeout_seconds", "Value": "60"}, {"Key": "access_logs.s3.prefix", "Value": ""}, {"Key": "access_logs.s3.bucket", "Value": ""}]}, "comments": {"input": {}, "output": {}}, "description": "This example enables deletion protection for the specified load balancer.", "id": "elbv2-modify-load-balancer-attributes-1", "title": "To enable deletion protection"}, {"input": {"Attributes": [{"Key": "idle_timeout.timeout_seconds", "Value": "30"}], "LoadBalancerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188"}, "output": {"Attributes": [{"Key": "idle_timeout.timeout_seconds", "Value": "30"}, {"Key": "access_logs.s3.enabled", "Value": "false"}, {"Key": "access_logs.s3.prefix", "Value": ""}, {"Key": "deletion_protection.enabled", "Value": "true"}, {"Key": "access_logs.s3.bucket", "Value": ""}]}, "comments": {"input": {}, "output": {}}, "description": "This example changes the idle timeout value for the specified load balancer.", "id": "elbv2-modify-load-balancer-attributes-2", "title": "To change the idle timeout"}, {"input": {"Attributes": [{"Key": "access_logs.s3.enabled", "Value": "true"}, {"Key": "access_logs.s3.bucket", "Value": "my-loadbalancer-logs"}, {"Key": "access_logs.s3.prefix", "Value": "myapp"}], "LoadBalancerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188"}, "output": {"Attributes": [{"Key": "access_logs.s3.enabled", "Value": "true"}, {"Key": "access_logs.s3.bucket", "Value": "my-load-balancer-logs"}, {"Key": "access_logs.s3.prefix", "Value": "myapp"}, {"Key": "idle_timeout.timeout_seconds", "Value": "60"}, {"Key": "deletion_protection.enabled", "Value": "false"}]}, "comments": {"input": {}, "output": {}}, "description": "This example enables access logs for the specified load balancer. Note that the S3 bucket must exist in the same region as the load balancer and must have a policy attached that grants access to the Elastic Load Balancing service.", "id": "elbv2-modify-load-balancer-attributes-3", "title": "To enable access logs"}], "ModifyRule": [{"input": {"Conditions": [{"Field": "path-pattern", "Values": ["/images/*"]}], "RuleArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:listener-rule/app/my-load-balancer/50dc6c495c0c9188/f2f7dc8efc522ab2/9683b2d02a6cabee"}, "output": {"Rules": [{"Actions": [{"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067", "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "Values": ["/images/*"]}], "IsDefault": false, "Priority": "10", "RuleArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:listener-rule/app/my-load-balancer/50dc6c495c0c9188/f2f7dc8efc522ab2/9683b2d02a6cabee"}]}, "comments": {"input": {}, "output": {}}, "description": "This example modifies the condition for the specified rule.", "id": "elbv2-modify-rule-1", "title": "To modify a rule"}], "ModifyTargetGroup": [{"input": {"HealthCheckPort": "443", "HealthCheckProtocol": "HTTPS", "TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-https-targets/2453ed029918f21f"}, "output": {"TargetGroups": [{"HealthCheckIntervalSeconds": 30, "HealthCheckPort": "443", "HealthCheckProtocol": "HTTPS", "HealthCheckTimeoutSeconds": 5, "HealthyThresholdCount": 5, "LoadBalancerArns": ["arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188"], "Matcher": {"HttpCode": "200"}, "Port": 443, "Protocol": "HTTPS", "TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-https-targets/2453ed029918f21f", "TargetGroupName": "my-https-targets", "UnhealthyThresholdCount": 2, "VpcId": "vpc-3ac0fb5f"}]}, "comments": {"input": {}, "output": {}}, "description": "This example changes the configuration of the health checks used to evaluate the health of the targets for the specified target group.", "id": "elbv2-modify-target-group-1", "title": "To modify the health check configuration for a target group"}], "ModifyTargetGroupAttributes": [{"input": {"Attributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "600"}], "TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067"}, "output": {"Attributes": [{"Key": "stickiness.enabled", "Value": "false"}, {"Key": "deregistration_delay.timeout_seconds", "Value": "600"}, {"Key": "stickiness.type", "Value": "lb_cookie"}, {"Key": "stickiness.lb_cookie.duration_seconds", "Value": "86400"}]}, "comments": {"input": {}, "output": {}}, "description": "This example sets the deregistration delay timeout to the specified value for the specified target group.", "id": "elbv2-modify-target-group-attributes-1", "title": "To modify the deregistration delay timeout"}], "RegisterTargets": [{"input": {"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067", "Targets": [{"Id": "i-80c8dd94"}, {"Id": "i-ceddcd4d"}]}, "comments": {"input": {}, "output": {}}, "description": "This example registers the specified instances with the specified target group.", "id": "elbv2-register-targets-1", "title": "To register targets with a target group"}, {"input": {"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-new-targets/3bb63f11dfb0faf9", "Targets": [{"Id": "i-80c8dd94", "Port": 80}, {"Id": "i-80c8dd94", "Port": 766}]}, "comments": {"input": {}, "output": {}}, "description": "This example registers the specified instance with the specified target group using multiple ports. This enables you to register ECS containers on the same instance as targets in the target group.", "id": "elbv2-register-targets-2", "title": "To register targets with a target group using port overrides"}], "RemoveTags": [{"input": {"ResourceArns": ["arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188"], "TagKeys": ["project", "department"]}, "comments": {"input": {}, "output": {}}, "description": "This example removes the specified tags from the specified load balancer.", "id": "elbv2-remove-tags-1", "title": "To remove tags from a load balancer"}], "SetRulePriorities": [{"input": {"RulePriorities": [{"Priority": 5, "RuleArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:listener-rule/app/my-load-balancer/50dc6c495c0c9188/f2f7dc8efc522ab2/1291d13826f405c3"}]}, "output": {"Rules": [{"Actions": [{"TargetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-targets/73e2d6bc24d8a067", "Type": "forward"}], "Conditions": [{"Field": "path-pattern", "Values": ["/img/*"]}], "IsDefault": false, "Priority": "5", "RuleArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:listener-rule/app/my-load-balancer/50dc6c495c0c9188/f2f7dc8efc522ab2/1291d13826f405c3"}]}, "comments": {"input": {}, "output": {}}, "description": "This example sets the priority of the specified rule.", "id": "elbv2-set-rule-priorities-1", "title": "To set the rule priority"}], "SetSecurityGroups": [{"input": {"LoadBalancerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188", "SecurityGroups": ["sg-5943793c"]}, "output": {"SecurityGroupIds": ["sg-5943793c"]}, "comments": {"input": {}, "output": {}}, "description": "This example associates the specified security group with the specified load balancer.", "id": "elbv2-set-security-groups-1", "title": "To associate a security group with a load balancer"}], "SetSubnets": [{"input": {"LoadBalancerArn": "arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-load-balancer/50dc6c495c0c9188", "Subnets": ["subnet-8360a9e7", "subnet-b7d581c0"]}, "output": {"AvailabilityZones": [{"SubnetId": "subnet-8360a9e7", "ZoneName": "us-west-2a"}, {"SubnetId": "subnet-b7d581c0", "ZoneName": "us-west-2b"}]}, "comments": {"input": {}, "output": {}}, "description": "This example enables the Availability Zones for the specified subnets for the specified load balancer.", "id": "elbv2-set-subnets-1", "title": "To enable Availability Zones for a load balancer"}]}}