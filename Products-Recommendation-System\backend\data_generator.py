import pandas as pd
import numpy as np
import random
from datetime import datetime, timedelta
import json
import uuid

class SyntheticDataGenerator:
    def __init__(self, n_users=1000, n_products=500, n_interactions=10000):
        self.n_users = n_users
        self.n_products = n_products
        self.n_interactions = n_interactions
        
        # Product categories and brands for realism
        self.categories = [
            'Electronics', 'Clothing', 'Books', 'Home & Kitchen', 'Sports',
            'Beauty', 'Toys', 'Automotive', 'Health', 'Jewelry'
        ]
        
        self.brands = [
            'BrandA', 'BrandB', 'BrandC', 'BrandD', 'BrandE',
            'BrandF', 'BrandG', 'BrandH', 'BrandI', 'BrandJ'
        ]
        
        self.event_types = ['view', 'click', 'add_to_cart', 'purchase', 'rating']
        self.event_weights = {'view': 1, 'click': 2, 'add_to_cart': 4, 'purchase': 8, 'rating': 5}
        
    def generate_users(self):
        """Generate synthetic user data"""
        users = []
        age_groups = ['18-24', '25-34', '35-44', '45-54', '55+']
        
        for i in range(self.n_users):
            user = {
                'user_id': f'u{i+1:04d}',
                'name': f'User {i+1}',
                'email': f'user{i+1}@example.com',
                'signup_date': (datetime.now() - timedelta(days=random.randint(1, 365))).strftime('%Y-%m-%d'),
                'age_group': random.choice(age_groups)
            }
            users.append(user)
            
        return pd.DataFrame(users)
    
    def generate_products(self):
        """Generate synthetic product data"""
        products = []
        
        for i in range(self.n_products):
            category = random.choice(self.categories)
            brand = random.choice(self.brands)
            
            # Generate realistic product names based on category
            product_names = {
                'Electronics': ['Smartphone', 'Laptop', 'Headphones', 'Camera', 'Tablet'],
                'Clothing': ['T-Shirt', 'Jeans', 'Dress', 'Jacket', 'Sneakers'],
                'Books': ['Novel', 'Cookbook', 'Biography', 'Guide', 'Textbook'],
                'Home & Kitchen': ['Blender', 'Coffee Maker', 'Pan Set', 'Vacuum', 'Lamp'],
                'Sports': ['Running Shoes', 'Yoga Mat', 'Dumbbells', 'Basketball', 'Bike'],
                'Beauty': ['Face Cream', 'Lipstick', 'Shampoo', 'Perfume', 'Serum'],
                'Toys': ['Action Figure', 'Board Game', 'Doll', 'Puzzle', 'Toy Car'],
                'Automotive': ['Car Tire', 'Oil Filter', 'Car Cover', 'GPS', 'Dash Cam'],
                'Health': ['Vitamins', 'Protein Powder', 'First Aid Kit', 'Thermometer', 'Scale'],
                'Jewelry': ['Necklace', 'Ring', 'Earrings', 'Watch', 'Bracelet']
            }
            
            base_name = random.choice(product_names.get(category, ['Product']))
            
            # Generate features based on category
            features_map = {
                'Electronics': ['wireless', 'bluetooth', 'waterproof', 'fast-charging'],
                'Clothing': ['cotton', 'polyester', 'slim-fit', 'casual'],
                'Books': ['paperback', 'hardcover', 'illustrated', 'bestseller'],
                'Home & Kitchen': ['stainless-steel', 'non-stick', 'dishwasher-safe', 'compact'],
                'Sports': ['lightweight', 'durable', 'ergonomic', 'professional'],
                'Beauty': ['organic', 'paraben-free', 'anti-aging', 'moisturizing'],
                'Toys': ['educational', 'safe', 'colorful', 'interactive'],
                'Automotive': ['universal', 'durable', 'weather-resistant', 'easy-install'],
                'Health': ['natural', 'clinically-tested', 'gluten-free', 'organic'],
                'Jewelry': ['sterling-silver', 'gold-plated', 'hypoallergenic', 'handcrafted']
            }
            
            features = random.sample(features_map.get(category, ['quality']), k=2)
            
            pid = f'p{i+1:04d}'
            product = {
                'product_id': pid,
                'title': f'{brand} {base_name}',
                'category': category,
                'brand': brand,
                'price': round(random.uniform(99, 4999), 2),
                'features': ','.join(features),
                'description': f'High-quality {base_name.lower()} from {brand}',
                # Deterministic placeholder image per product
                'image_url': f'https://picsum.photos/seed/{pid}/600/400'
            }
            products.append(product)
            
        return pd.DataFrame(products)
    
    def generate_interactions(self, users_df, products_df):
        """Generate synthetic interaction data with realistic patterns"""
        interactions = []
        
        # Create some user preferences (some users prefer certain categories)
        user_preferences = {}
        for user_id in users_df['user_id']:
            # Each user has preference for 2-3 categories
            preferred_categories = random.sample(self.categories, k=random.randint(2, 3))
            user_preferences[user_id] = preferred_categories
        
        for _ in range(self.n_interactions):
            user_id = random.choice(users_df['user_id'].tolist())
            
            # 70% chance to pick from preferred categories, 30% random
            if random.random() < 0.7 and user_id in user_preferences:
                preferred_cats = user_preferences[user_id]
                available_products = products_df[products_df['category'].isin(preferred_cats)]
                if len(available_products) > 0:
                    product_id = random.choice(available_products['product_id'].tolist())
                else:
                    product_id = random.choice(products_df['product_id'].tolist())
            else:
                product_id = random.choice(products_df['product_id'].tolist())
            
            event_type = random.choice(self.event_types)
            
            # For ratings, use 1-5 scale, otherwise use event weights
            if event_type == 'rating':
                value = random.randint(1, 5)
            else:
                value = self.event_weights[event_type]
            
            # Generate realistic timestamp (last 6 months)
            timestamp = datetime.now() - timedelta(days=random.randint(1, 180))
            
            interaction = {
                'user_id': user_id,
                'product_id': product_id,
                'event_type': event_type,
                'value': value,
                'timestamp': timestamp.isoformat()
            }
            interactions.append(interaction)
        
        return pd.DataFrame(interactions)
    
    def generate_all_data(self):
        """Generate complete synthetic dataset"""
        print("Generating users...")
        users_df = self.generate_users()
        
        print("Generating products...")
        products_df = self.generate_products()
        
        print("Generating interactions...")
        interactions_df = self.generate_interactions(users_df, products_df)
        
        return users_df, products_df, interactions_df

if __name__ == "__main__":
    generator = SyntheticDataGenerator(n_users=1000, n_products=500, n_interactions=10000)
    users_df, products_df, interactions_df = generator.generate_all_data()
    
    # Save to CSV files
    users_df.to_csv('/app/backend/data/users_sample.csv', index=False)
    products_df.to_csv('/app/backend/data/products_sample.csv', index=False)
    interactions_df.to_csv('/app/backend/data/interactions_sample.csv', index=False)
    
    print(f"Generated {len(users_df)} users, {len(products_df)} products, {len(interactions_df)} interactions")