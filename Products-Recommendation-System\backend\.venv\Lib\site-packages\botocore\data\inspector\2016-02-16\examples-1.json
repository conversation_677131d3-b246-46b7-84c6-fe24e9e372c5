{"version": "1.0", "examples": {"AddAttributesToFindings": [{"input": {"attributes": [{"key": "Example", "value": "example"}], "findingArns": ["arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-8l1VIE0D/run/0-Z02cjjug/finding/0-T8yM9mEU"]}, "output": {"failedItems": {}}, "comments": {"input": {}, "output": {}}, "description": "Assigns attributes (key and value pairs) to the findings that are specified by the ARNs of the findings.", "id": "add-attributes-to-findings-1481063856401", "title": "Add attributes to findings"}], "CreateAssessmentTarget": [{"input": {"assessmentTargetName": "ExampleAsses<PERSON>ent<PERSON>arget", "resourceGroupArn": "arn:aws:inspector:us-west-2:************:resourcegroup/0-AB6DMKnv"}, "output": {"assessmentTargetArn": "arn:aws:inspector:us-west-2:************:target/0-nvgVhaxX"}, "comments": {"input": {}, "output": {}}, "description": "Creates a new assessment target using the ARN of the resource group that is generated by CreateResourceGroup. You can create up to 50 assessment targets per AWS account. You can run up to 500 concurrent agents per AWS account.", "id": "create-assessment-target-*************", "title": "Create assessment target"}], "CreateAssessmentTemplate": [{"input": {"assessmentTargetArn": "arn:aws:inspector:us-west-2:************:target/0-nvgVhaxX", "assessmentTemplateName": "ExampleAssessmentTemplate", "durationInSeconds": 180, "rulesPackageArns": ["arn:aws:inspector:us-west-2:************:rulespackage/0-11B9DBXp"], "userAttributesForFindings": [{"key": "Example", "value": "example"}]}, "output": {"assessmentTemplateArn": "arn:aws:inspector:us-west-2:************:target/0-nvgVhaxX/template/0-it5r2S4T"}, "comments": {"input": {}, "output": {}}, "description": "Creates an assessment template for the assessment target that is specified by the ARN of the assessment target.", "id": "create-assessment-template-*************", "title": "Create assessment template"}], "CreateResourceGroup": [{"input": {"resourceGroupTags": [{"key": "Name", "value": "example"}]}, "output": {"resourceGroupArn": "arn:aws:inspector:us-west-2:************:resourcegroup/0-AB6DMKnv"}, "comments": {"input": {}, "output": {}}, "description": "Creates a resource group using the specified set of tags (key and value pairs) that are used to select the EC2 instances to be included in an Amazon Inspector assessment target. The created resource group is then used to create an Amazon Inspector assessment target. ", "id": "create-resource-group-1481064169037", "title": "Create resource group"}], "DeleteAssessmentRun": [{"input": {"assessmentRunArn": "arn:aws:inspector:us-west-2:************:target/0-nvgVhaxX/template/0-it5r2S4T/run/0-11LMTAVe"}, "comments": {"input": {}, "output": {}}, "description": "Deletes the assessment run that is specified by the ARN of the assessment run.", "id": "delete-assessment-run-1481064251629", "title": "Delete assessment run"}], "DeleteAssessmentTarget": [{"input": {"assessmentTargetArn": "arn:aws:inspector:us-west-2:************:target/0-0kFIPusq"}, "comments": {"input": {}, "output": {}}, "description": "Deletes the assessment target that is specified by the ARN of the assessment target.", "id": "delete-assessment-target-1481064309029", "title": "Delete assessment target"}], "DeleteAssessmentTemplate": [{"input": {"assessmentTemplateArn": "arn:aws:inspector:us-west-2:************:target/0-nvgVhaxX/template/0-it5r2S4T"}, "comments": {"input": {}, "output": {}}, "description": "Deletes the assessment template that is specified by the ARN of the assessment template.", "id": "delete-assessment-template-1481064364074", "title": "Delete assessment template"}], "DescribeAssessmentRuns": [{"input": {"assessmentRunArns": ["arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw/run/0-MKkpXXPE"]}, "output": {"assessmentRuns": [{"name": "Run 1 for ExampleAssessmentTemplate", "arn": "arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw/run/0-MKkpXXPE", "assessmentTemplateArn": "arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw", "completedAt": "**********.4", "createdAt": "1458680170.035", "dataCollected": true, "durationInSeconds": 3600, "findingCounts": {"High": 14, "Informational": 0, "Low": 0, "Medium": 2, "Undefined": 0}, "notifications": [], "rulesPackageArns": ["arn:aws:inspector:us-west-2:************:rulespackage/0-X1KXtawP"], "startedAt": "1458680170.161", "state": "COMPLETED", "stateChangedAt": "**********.4", "stateChanges": [{"state": "CREATED", "stateChangedAt": "1458680170.035"}, {"state": "START_DATA_COLLECTION_PENDING", "stateChangedAt": "1458680170.065"}, {"state": "START_DATA_COLLECTION_IN_PROGRESS", "stateChangedAt": "1458680170.096"}, {"state": "COLLECTING_DATA", "stateChangedAt": "1458680170.161"}, {"state": "STOP_DATA_COLLECTION_PENDING", "stateChangedAt": "1458680239.883"}, {"state": "DATA_COLLECTED", "stateChangedAt": "1458680299.847"}, {"state": "EVALUATING_RULES", "stateChangedAt": "1458680300.099"}, {"state": "COMPLETED", "stateChangedAt": "**********.4"}], "userAttributesForFindings": []}], "failedItems": {}}, "comments": {"input": {}, "output": {}}, "description": "Describes the assessment runs that are specified by the ARNs of the assessment runs.", "id": "describte-assessment-runs-1481064424352", "title": "Describte assessment runs"}], "DescribeAssessmentTargets": [{"input": {"assessmentTargetArns": ["arn:aws:inspector:us-west-2:************:target/0-0kFIPusq"]}, "output": {"assessmentTargets": [{"name": "ExampleAsses<PERSON>ent<PERSON>arget", "arn": "arn:aws:inspector:us-west-2:************:target/0-0kFIPusq", "createdAt": "**********.459", "resourceGroupArn": "arn:aws:inspector:us-west-2:************:resourcegroup/0-PyGXopAI", "updatedAt": "**********.459"}], "failedItems": {}}, "comments": {"input": {}, "output": {}}, "description": "Describes the assessment targets that are specified by the ARNs of the assessment targets.", "id": "describte-assessment-targets-1481064527735", "title": "Describte assessment targets"}], "DescribeAssessmentTemplates": [{"input": {"assessmentTemplateArns": ["arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw"]}, "output": {"assessmentTemplates": [{"name": "ExampleAssessmentTemplate", "arn": "arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw", "assessmentRunCount": 0, "assessmentTargetArn": "arn:aws:inspector:us-west-2:************:target/0-0kFIPusq", "createdAt": "**********.844", "durationInSeconds": 3600, "rulesPackageArns": ["arn:aws:inspector:us-west-2:************:rulespackage/0-X1KXtawP"], "userAttributesForFindings": []}], "failedItems": {}}, "comments": {"input": {}, "output": {}}, "description": "Describes the assessment templates that are specified by the ARNs of the assessment templates.", "id": "describte-assessment-templates-*************", "title": "Describte assessment templates"}], "DescribeCrossAccountAccessRole": [{"output": {"registeredAt": "**********.826", "roleArn": "arn:aws:iam::************:role/inspector", "valid": true}, "comments": {"input": {}, "output": {}}, "description": "Describes the IAM role that enables Amazon Inspector to access your AWS account.", "id": "describte-cross-account-access-role-*************", "title": "Describte cross account access role"}], "DescribeFindings": [{"input": {"findingArns": ["arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw/run/0-MKkpXXPE/finding/0-HwPnsDm4"]}, "output": {"failedItems": {}, "findings": [{"arn": "arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw/run/0-MKkpXXPE/finding/0-HwPnsDm4", "assetAttributes": {"ipv4Addresses": [], "schemaVersion": 1}, "assetType": "ec2-instance", "attributes": [], "confidence": 10, "createdAt": "**********.37", "description": "Amazon Inspector did not find any potential security issues during this assessment.", "indicatorOfCompromise": false, "numericSeverity": 0, "recommendation": "No remediation needed.", "schemaVersion": 1, "service": "Inspector", "serviceAttributes": {"assessmentRunArn": "arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw/run/0-MKkpXXPE", "rulesPackageArn": "arn:aws:inspector:us-west-2:************:rulespackage/0-X1KXtawP", "schemaVersion": 1}, "severity": "Informational", "title": "No potential security issues found", "updatedAt": "**********.37", "userAttributes": []}]}, "comments": {"input": {}, "output": {}}, "description": "Describes the findings that are specified by the ARNs of the findings.", "id": "describte-findings-1481064771803", "title": "Describe findings"}], "DescribeResourceGroups": [{"input": {"resourceGroupArns": ["arn:aws:inspector:us-west-2:************:resourcegroup/0-PyGXopAI"]}, "output": {"failedItems": {}, "resourceGroups": [{"arn": "arn:aws:inspector:us-west-2:************:resourcegroup/0-PyGXopAI", "createdAt": "**********.098", "tags": [{"key": "Name", "value": "example"}]}]}, "comments": {"input": {}, "output": {}}, "description": "Describes the resource groups that are specified by the ARNs of the resource groups.", "id": "describe-resource-groups-1481065787743", "title": "Describe resource groups"}], "DescribeRulesPackages": [{"input": {"rulesPackageArns": ["arn:aws:inspector:us-west-2:************:rulespackage/0-JJOtZiqQ"]}, "output": {"failedItems": {}, "rulesPackages": [{"version": "1.1", "name": "Security Best Practices", "arn": "arn:aws:inspector:us-west-2:************:rulespackage/0-JJOtZiqQ", "description": "The rules in this package help determine whether your systems are configured securely.", "provider": "Amazon Web Services, Inc."}]}, "comments": {"input": {}, "output": {}}, "description": "Describes the rules packages that are specified by the ARNs of the rules packages.", "id": "describe-rules-packages-1481069641979", "title": "Describe rules packages"}], "GetTelemetryMetadata": [{"input": {"assessmentRunArn": "arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw/run/0-MKkpXXPE"}, "output": {"telemetryMetadata": [{"count": 2, "dataSize": 345, "messageType": "InspectorDuplicateProcess"}, {"count": 3, "dataSize": 255, "messageType": "InspectorTimeEventMsg"}, {"count": 4, "dataSize": 1082, "messageType": "InspectorNetworkInterface"}, {"count": 2, "dataSize": 349, "messageType": "InspectorDnsEntry"}, {"count": 11, "dataSize": 2514, "messageType": "InspectorDirectoryInfoMsg"}, {"count": 1, "dataSize": 179, "messageType": "InspectorTcpV6ListeningPort"}, {"count": 101, "dataSize": 10949, "messageType": "InspectorTerminal"}, {"count": 26, "dataSize": 5916, "messageType": "InspectorUser"}, {"count": 282, "dataSize": 32148, "messageType": "InspectorDynamicallyLoadedCodeModule"}, {"count": 18, "dataSize": 10172, "messageType": "InspectorCreateProcess"}, {"count": 3, "dataSize": 8001, "messageType": "InspectorProcessPerformance"}, {"count": 1, "dataSize": 360, "messageType": "InspectorOperatingSystem"}, {"count": 6, "dataSize": 546, "messageType": "InspectorStopProcess"}, {"count": 1, "dataSize": 1553, "messageType": "InspectorInstanceMetaData"}, {"count": 2, "dataSize": 434, "messageType": "InspectorTcpV4Connection"}, {"count": 474, "dataSize": 2960322, "messageType": "InspectorPackageInfo"}, {"count": 3, "dataSize": 2235, "messageType": "InspectorSystemPerformance"}, {"count": 105, "dataSize": 46048, "messageType": "InspectorCodeModule"}, {"count": 1, "dataSize": 182, "messageType": "InspectorUdpV6ListeningPort"}, {"count": 2, "dataSize": 371, "messageType": "InspectorUdpV4ListeningPort"}, {"count": 18, "dataSize": 8362, "messageType": "InspectorKernelModule"}, {"count": 29, "dataSize": 48788, "messageType": "InspectorConfigurationInfo"}, {"count": 1, "dataSize": 79, "messageType": "InspectorMonitoringStart"}, {"count": 5, "dataSize": 0, "messageType": "InspectorSplitMsgBegin"}, {"count": 51, "dataSize": 4593, "messageType": "InspectorGroup"}, {"count": 1, "dataSize": 184, "messageType": "InspectorTcpV4ListeningPort"}, {"count": 1159, "dataSize": 3146579, "messageType": "Total"}, {"count": 5, "dataSize": 0, "messageType": "InspectorSplitMsgEnd"}, {"count": 1, "dataSize": 612, "messageType": "InspectorLoadImageInProcess"}]}, "comments": {"input": {}, "output": {}}, "description": "Information about the data that is collected for the specified assessment run.", "id": "get-telemetry-metadata-1481066021297", "title": "Get telemetry metadata"}], "ListAssessmentRunAgents": [{"input": {"assessmentRunArn": "arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw/run/0-MKkpXXPE", "maxResults": 123}, "output": {"assessmentRunAgents": [{"agentHealth": "HEALTHY", "agentHealthCode": "RUNNING", "agentId": "i-49113b93", "assessmentRunArn": "arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw/run/0-MKkpXXPE", "telemetryMetadata": [{"count": 2, "dataSize": 345, "messageType": "InspectorDuplicateProcess"}, {"count": 3, "dataSize": 255, "messageType": "InspectorTimeEventMsg"}, {"count": 4, "dataSize": 1082, "messageType": "InspectorNetworkInterface"}, {"count": 2, "dataSize": 349, "messageType": "InspectorDnsEntry"}, {"count": 11, "dataSize": 2514, "messageType": "InspectorDirectoryInfoMsg"}, {"count": 1, "dataSize": 179, "messageType": "InspectorTcpV6ListeningPort"}, {"count": 101, "dataSize": 10949, "messageType": "InspectorTerminal"}, {"count": 26, "dataSize": 5916, "messageType": "InspectorUser"}, {"count": 282, "dataSize": 32148, "messageType": "InspectorDynamicallyLoadedCodeModule"}, {"count": 18, "dataSize": 10172, "messageType": "InspectorCreateProcess"}, {"count": 3, "dataSize": 8001, "messageType": "InspectorProcessPerformance"}, {"count": 1, "dataSize": 360, "messageType": "InspectorOperatingSystem"}, {"count": 6, "dataSize": 546, "messageType": "InspectorStopProcess"}, {"count": 1, "dataSize": 1553, "messageType": "InspectorInstanceMetaData"}, {"count": 2, "dataSize": 434, "messageType": "InspectorTcpV4Connection"}, {"count": 474, "dataSize": 2960322, "messageType": "InspectorPackageInfo"}, {"count": 3, "dataSize": 2235, "messageType": "InspectorSystemPerformance"}, {"count": 105, "dataSize": 46048, "messageType": "InspectorCodeModule"}, {"count": 1, "dataSize": 182, "messageType": "InspectorUdpV6ListeningPort"}, {"count": 2, "dataSize": 371, "messageType": "InspectorUdpV4ListeningPort"}, {"count": 18, "dataSize": 8362, "messageType": "InspectorKernelModule"}, {"count": 29, "dataSize": 48788, "messageType": "InspectorConfigurationInfo"}, {"count": 1, "dataSize": 79, "messageType": "InspectorMonitoringStart"}, {"count": 5, "dataSize": 0, "messageType": "InspectorSplitMsgBegin"}, {"count": 51, "dataSize": 4593, "messageType": "InspectorGroup"}, {"count": 1, "dataSize": 184, "messageType": "InspectorTcpV4ListeningPort"}, {"count": 1159, "dataSize": 3146579, "messageType": "Total"}, {"count": 5, "dataSize": 0, "messageType": "InspectorSplitMsgEnd"}, {"count": 1, "dataSize": 612, "messageType": "InspectorLoadImageInProcess"}]}], "nextToken": "1"}, "comments": {"input": {}, "output": {}}, "description": "Lists the agents of the assessment runs that are specified by the ARNs of the assessment runs.", "id": "list-assessment-run-agents-1481918140642", "title": "List assessment run agents"}], "ListAssessmentRuns": [{"input": {"assessmentTemplateArns": ["arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw"], "maxResults": 123}, "output": {"assessmentRunArns": ["arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw/run/0-MKkpXXPE", "arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw/run/0-v5D6fI3v"], "nextToken": "1"}, "comments": {"input": {}, "output": {}}, "description": "Lists the assessment runs that correspond to the assessment templates that are specified by the ARNs of the assessment templates.", "id": "list-assessment-runs-*************", "title": "List assessment runs"}], "ListAssessmentTargets": [{"input": {"maxResults": 123}, "output": {"assessmentTargetArns": ["arn:aws:inspector:us-west-2:************:target/0-0kFIPusq"], "nextToken": "1"}, "comments": {"input": {}, "output": {}}, "description": "Lists the ARNs of the assessment targets within this AWS account. ", "id": "list-assessment-targets-*************", "title": "List assessment targets"}], "ListAssessmentTemplates": [{"input": {"assessmentTargetArns": ["arn:aws:inspector:us-west-2:************:target/0-0kFIPusq"], "maxResults": 123}, "output": {"assessmentTemplateArns": ["arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw", "arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-Uza6ihLh"], "nextToken": "1"}, "comments": {"input": {}, "output": {}}, "description": "Lists the assessment templates that correspond to the assessment targets that are specified by the ARNs of the assessment targets.", "id": "list-assessment-templates-*************", "title": "List assessment templates"}], "ListEventSubscriptions": [{"input": {"maxResults": 123, "resourceArn": "arn:aws:inspector:us-west-2:************:target/0-nvgVhaxX/template/0-7sbz2Kz0"}, "output": {"nextToken": "1", "subscriptions": [{"eventSubscriptions": [{"event": "ASSESSMENT_RUN_COMPLETED", "subscribedAt": "1459455440.867"}], "resourceArn": "arn:aws:inspector:us-west-2:************:target/0-nvgVhaxX/template/0-7sbz2Kz0", "topicArn": "arn:aws:sns:us-west-2:************:exampletopic"}]}, "comments": {"input": {}, "output": {}}, "description": "Lists all the event subscriptions for the assessment template that is specified by the ARN of the assessment template. ", "id": "list-event-subscriptions-1481068376945", "title": "List event subscriptions"}], "ListFindings": [{"input": {"assessmentRunArns": ["arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw/run/0-MKkpXXPE"], "maxResults": 123}, "output": {"findingArns": ["arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw/run/0-MKkpXXPE/finding/0-HwPnsDm4", "arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-4r1V2mAw/run/0-v5D6fI3v/finding/0-tyvmqBLy"], "nextToken": "1"}, "comments": {"input": {}, "output": {}}, "description": "Lists findings that are generated by the assessment runs that are specified by the ARNs of the assessment runs.", "id": "list-findings-1481066840611", "title": "List findings"}], "ListRulesPackages": [{"input": {"maxResults": 123}, "output": {"nextToken": "1", "rulesPackageArns": ["arn:aws:inspector:us-west-2:************:rulespackage/0-9hgA516p", "arn:aws:inspector:us-west-2:************:rulespackage/0-H5hpSawc", "arn:aws:inspector:us-west-2:************:rulespackage/0-JJOtZiqQ", "arn:aws:inspector:us-west-2:************:rulespackage/0-vg5GGHSD"]}, "comments": {"input": {}, "output": {}}, "description": "Lists all available Amazon Inspector rules packages.", "id": "list-rules-packages-1481066954883", "title": "List rules packages"}], "ListTagsForResource": [{"input": {"resourceArn": "arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-gcwFliYu"}, "output": {"tags": [{"key": "Name", "value": "Example"}]}, "comments": {"input": {}, "output": {}}, "description": "Lists all tags associated with an assessment template.", "id": "list-tags-for-resource-*************", "title": "List tags for resource"}], "PreviewAgents": [{"input": {"maxResults": 123, "previewAgentsArn": "arn:aws:inspector:us-west-2:************:target/0-0kFIPusq"}, "output": {"agentPreviews": [{"agentId": "i-49113b93"}], "nextToken": "1"}, "comments": {"input": {}, "output": {}}, "description": "Previews the agents installed on the EC2 instances that are part of the specified assessment target.", "id": "preview-agents-*************", "title": "Preview agents"}], "RegisterCrossAccountAccessRole": [{"input": {"roleArn": "arn:aws:iam::************:role/inspector"}, "comments": {"input": {}, "output": {}}, "description": "Registers the IAM role that Amazon Inspector uses to list your EC2 instances at the start of the assessment run or when you call the PreviewAgents action.", "id": "register-cross-account-access-role-*************", "title": "Register cross account access role"}], "RemoveAttributesFromFindings": [{"input": {"attributeKeys": ["key=Example,value=example"], "findingArns": ["arn:aws:inspector:us-west-2:************:target/0-0kFIPusq/template/0-8l1VIE0D/run/0-Z02cjjug/finding/0-T8yM9mEU"]}, "output": {"failedItems": {}}, "comments": {"input": {}, "output": {}}, "description": "Removes entire attributes (key and value pairs) from the findings that are specified by the ARNs of the findings where an attribute with the specified key exists.", "id": "remove-attributes-from-findings-*************", "title": "Remove attributes from findings"}], "SetTagsForResource": [{"input": {"resourceArn": "arn:aws:inspector:us-west-2:************:target/0-nvgVhaxX/template/0-7sbz2Kz0", "tags": [{"key": "Example", "value": "example"}]}, "comments": {"input": {}, "output": {}}, "description": "Sets tags (key and value pairs) to the assessment template that is specified by the ARN of the assessment template.", "id": "set-tags-for-resource-**********646", "title": "Set tags for resource"}], "StartAssessmentRun": [{"input": {"assessmentRunName": "examplerun", "assessmentTemplateArn": "arn:aws:inspector:us-west-2:************:target/0-nvgVhaxX/template/0-it5r2S4T"}, "output": {"assessmentRunArn": "arn:aws:inspector:us-west-2:************:target/0-nvgVhaxX/template/0-it5r2S4T/run/0-jOoroxyY"}, "comments": {"input": {}, "output": {}}, "description": "Starts the assessment run specified by the ARN of the assessment template. For this API to function properly, you must not exceed the limit of running up to 500 concurrent agents per AWS account.", "id": "start-assessment-run-*************", "title": "Start assessment run"}], "StopAssessmentRun": [{"input": {"assessmentRunArn": "arn:aws:inspector:us-west-2:************:target/0-nvgVhaxX/template/0-it5r2S4T/run/0-11LMTAVe"}, "comments": {"input": {}, "output": {}}, "description": "Stops the assessment run that is specified by the ARN of the assessment run.", "id": "stop-assessment-run-*************", "title": "Stop assessment run"}], "SubscribeToEvent": [{"input": {"event": "ASSESSMENT_RUN_COMPLETED", "resourceArn": "arn:aws:inspector:us-west-2:************:target/0-nvgVhaxX/template/0-7sbz2Kz0", "topicArn": "arn:aws:sns:us-west-2:************:exampletopic"}, "comments": {"input": {}, "output": {}}, "description": "Enables the process of sending Amazon Simple Notification Service (SNS) notifications about a specified event to a specified SNS topic.", "id": "subscribe-to-event-*************", "title": "Subscribe to event"}], "UnsubscribeFromEvent": [{"input": {"event": "ASSESSMENT_RUN_COMPLETED", "resourceArn": "arn:aws:inspector:us-west-2:************:target/0-nvgVhaxX/template/0-7sbz2Kz0", "topicArn": "arn:aws:sns:us-west-2:************:exampletopic"}, "comments": {"input": {}, "output": {}}, "description": "Disables the process of sending Amazon Simple Notification Service (SNS) notifications about a specified event to a specified SNS topic.", "id": "unsubscribe-from-event-1481067781705", "title": "Unsubscribe from event"}], "UpdateAssessmentTarget": [{"input": {"assessmentTargetArn": "arn:aws:inspector:us-west-2:************:target/0-nvgVhaxX", "assessmentTargetName": "Example", "resourceGroupArn": "arn:aws:inspector:us-west-2:************:resourcegroup/0-yNbgL5Pt"}, "comments": {"input": {}, "output": {}}, "description": "Updates the assessment target that is specified by the ARN of the assessment target.", "id": "update-assessment-target-1481067866692", "title": "Update assessment target"}]}}