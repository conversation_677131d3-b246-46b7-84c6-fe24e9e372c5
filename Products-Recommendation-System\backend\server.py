from fastapi import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from dotenv import load_dotenv
from starlette.middleware.cors import CORSMiddleware
from motor.motor_asyncio import AsyncIOMotorClient
import os
import logging
from pathlib import Path
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime, timedelta
import jwt
import bcrypt
import asyncio
from recommendation_engine import CollaborativeFilteringEngine
from data_generator import SyntheticDataGenerator

ROOT_DIR = Path(__file__).parent
load_dotenv(ROOT_DIR / '.env')

# MongoDB connection
mongo_url = os.environ['MONGO_URL']
client = AsyncIOMotorClient(mongo_url)
db = client[os.environ['DB_NAME']]

# JWT Configuration
JWT_SECRET = os.environ.get('JWT_SECRET', 'your-secret-key-change-in-production')
JWT_ALGORITHM = 'HS256'
JWT_EXPIRATION_HOURS = 24

# Initialize recommendation engine
recommendation_engine = CollaborativeFilteringEngine()

# Create the main app
app = FastAPI(title="ML Recommendation System", version="1.0.0")
api_router = APIRouter(prefix="/api")

# Security
security = HTTPBearer()

# Pydantic Models
class User(BaseModel):
    user_id: str = Field(default_factory=lambda: f"u{str(uuid.uuid4())[:8]}")
    name: str
    email: str
    signup_date: datetime = Field(default_factory=datetime.utcnow)
    age_group: Optional[str] = None

class UserCreate(BaseModel):
    name: str
    email: str
    password: str
    age_group: Optional[str] = None

class UserLogin(BaseModel):
    email: str
    password: str

class Product(BaseModel):
    product_id: str = Field(default_factory=lambda: f"p{str(uuid.uuid4())[:8]}")
    title: str
    category: str
    brand: str
    price: float
    features: str
    description: str
    image_url: Optional[str] = None

class Interaction(BaseModel):
    user_id: str
    product_id: str
    event_type: str  # view, click, add_to_cart, purchase, rating
    value: float
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class InteractionCreate(BaseModel):
    product_id: str
    event_type: str
    value: float = 1.0

class RecommendationResponse(BaseModel):
    product_id: str
    title: str
    category: str
    brand: str
    price: float
    score: float

class AnalyticsResponse(BaseModel):
    category_distribution: Dict[str, int]
    popular_products: List[Dict[str, Any]]
    daily_activity: Dict[str, int]
    total_users: int
    total_products: int
    total_interactions: int

# Authentication Functions
def hash_password(password: str) -> str:
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def verify_password(password: str, hashed: str) -> bool:
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def create_access_token(data: dict) -> str:
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS)
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, JWT_SECRET, algorithm=JWT_ALGORITHM)

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        token = credentials.credentials
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials"
            )
        return user_id
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials"
        )

# Initialize system data
async def initialize_system():
    """Initialize the system with synthetic data and train models"""
    try:
        # Check if data already exists
        user_count = await db.users.count_documents({})
        if user_count > 0:
            logger.info("Data already exists, loading existing data")
        else:
            logger.info("Generating synthetic data...")
            generator = SyntheticDataGenerator(n_users=1000, n_products=500, n_interactions=10000)
            users_df, products_df, interactions_df = generator.generate_all_data()
            
            # Save to MongoDB
            await db.users.insert_many(users_df.to_dict('records'))
            await db.products.insert_many(products_df.to_dict('records'))
            await db.interactions.insert_many(interactions_df.to_dict('records'))
            
            logger.info("Synthetic data saved to MongoDB")
        
        # Load data and train models
        logger.info("Training recommendation models...")
        
        # Get data from MongoDB
        users_data = await db.users.find().to_list(None)
        products_data = await db.products.find().to_list(None)
        interactions_data = await db.interactions.find().to_list(None)
        
        # Convert to DataFrames (in production, you'd load from files or keep in memory)
        import pandas as pd
        recommendation_engine.users_df = pd.DataFrame(users_data)
        recommendation_engine.products_df = pd.DataFrame(products_data)
        recommendation_engine.interactions_df = pd.DataFrame(interactions_data)
        
        # Create mappings
        unique_users = recommendation_engine.users_df['user_id'].unique()
        unique_products = recommendation_engine.products_df['product_id'].unique()
        
        recommendation_engine.user_to_idx = {user: idx for idx, user in enumerate(unique_users)}
        recommendation_engine.product_to_idx = {product: idx for idx, product in enumerate(unique_products)}
        recommendation_engine.idx_to_user = {idx: user for user, idx in recommendation_engine.user_to_idx.items()}
        recommendation_engine.idx_to_product = {idx: product for product, idx in recommendation_engine.product_to_idx.items()}
        
        # Preprocess and train models
        if recommendation_engine.preprocess_interactions():
            recommendation_engine.train_item_based_cf()
            recommendation_engine.train_svd_model()
            logger.info("Recommendation models trained successfully")
        
    except Exception as e:
        logger.error(f"Error initializing system: {e}")

# API Endpoints
@api_router.post("/auth/signup")
async def signup(user_data: UserCreate):
    try:
        # Check if user already exists
        existing_user = await db.users.find_one({"email": user_data.email})
        if existing_user:
            raise HTTPException(status_code=400, detail="User already exists")
        
        # Create new user
        hashed_password = hash_password(user_data.password)
        user = User(
            name=user_data.name,
            email=user_data.email,
            age_group=user_data.age_group
        )
        
        # Save user and password separately for security
        user_dict = user.dict()
        await db.users.insert_one(user_dict)
        await db.user_passwords.insert_one({
            "user_id": user.user_id,
            "password_hash": hashed_password
        })
        
        # Create access token
        access_token = create_access_token(data={"sub": user.user_id})
        
        # Remove MongoDB _id from response
        user_dict.pop('_id', None)
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": user_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Signup error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@api_router.post("/auth/login")
async def login(user_data: UserLogin):
    try:
        # Find user
        user = await db.users.find_one({"email": user_data.email})
        if not user:
            raise HTTPException(status_code=401, detail="Invalid credentials")
        
        # Check password
        user_password = await db.user_passwords.find_one({"user_id": user["user_id"]})
        if not user_password or not verify_password(user_data.password, user_password["password_hash"]):
            raise HTTPException(status_code=401, detail="Invalid credentials")
        
        # Create access token
        access_token = create_access_token(data={"sub": user["user_id"]})
        
        # Remove MongoDB _id from response
        user_response = {k: v for k, v in user.items() if k != '_id'}
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": user_response
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@api_router.get("/products", response_model=List[Product])
async def get_products(category: Optional[str] = None, limit: int = 50):
    try:
        query = {}
        if category:
            query["category"] = category
        
        products = await db.products.find(query).limit(limit).to_list(None)
        return [Product(**product) for product in products]
    except Exception as e:
        logger.error(f"Error getting products: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@api_router.get("/products/{product_id}", response_model=Product)
async def get_product(product_id: str):
    try:
        product = await db.products.find_one({"product_id": product_id})
        if not product:
            raise HTTPException(status_code=404, detail="Product not found")
        return Product(**product)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting product: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@api_router.get("/recommendations", response_model=List[RecommendationResponse])
async def get_recommendations(
    current_user: str = Depends(get_current_user),
    n: int = 10,
    method: str = "item_based"
):
    try:
        if method == "svd":
            recommendations = recommendation_engine.get_svd_recommendations(current_user, n)
        else:  # default to item_based
            recommendations = recommendation_engine.get_item_based_recommendations(current_user, n)
        
        return [RecommendationResponse(**rec) for rec in recommendations]
    except Exception as e:
        logger.error(f"Error getting recommendations: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@api_router.post("/interactions")
async def create_interaction(
    interaction_data: InteractionCreate,
    current_user: str = Depends(get_current_user)
):
    try:
        interaction = Interaction(
            user_id=current_user,
            product_id=interaction_data.product_id,
            event_type=interaction_data.event_type,
            value=interaction_data.value
        )
        
        await db.interactions.insert_one(interaction.dict())
        
        return {"message": "Interaction recorded successfully"}
    except Exception as e:
        logger.error(f"Error creating interaction: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@api_router.get("/user/history")
async def get_user_history(current_user: str = Depends(get_current_user)):
    try:
        interactions = await db.interactions.find({"user_id": current_user}).to_list(None)
        
        # Get product details
        product_ids = [interaction["product_id"] for interaction in interactions]
        products = await db.products.find({"product_id": {"$in": product_ids}}).to_list(None)
        product_dict = {p["product_id"]: p for p in products}
        
        # Combine interaction and product data, removing MongoDB _id fields
        history = []
        for interaction in interactions:
            product = product_dict.get(interaction["product_id"])
            if product:
                # Remove MongoDB _id fields to avoid serialization issues
                clean_interaction = {k: v for k, v in interaction.items() if k != '_id'}
                clean_product = {k: v for k, v in product.items() if k != '_id'}
                
                history.append({
                    "interaction": clean_interaction,
                    "product": clean_product
                })
        
        return history
    except Exception as e:
        logger.error(f"Error getting user history: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@api_router.get("/analytics", response_model=AnalyticsResponse)
async def get_analytics():
    try:
        analytics_data = recommendation_engine.get_analytics_data()
        return AnalyticsResponse(**analytics_data)
    except Exception as e:
        logger.error(f"Error getting analytics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@api_router.get("/categories")
async def get_categories():
    try:
        categories = await db.products.distinct("category")
        return {"categories": categories}
    except Exception as e:
        logger.error(f"Error getting categories: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Health check
@api_router.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.utcnow()}

# Include router
app.include_router(api_router)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=os.environ.get('CORS_ORIGINS', '*').split(','),
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Startup event
@app.on_event("startup")
async def startup_event():
    # Run heavy initialization in background to avoid boot timeouts on hosts like Render
    asyncio.create_task(initialize_system())

@app.on_event("shutdown")
async def shutdown_db_client():
    client.close()