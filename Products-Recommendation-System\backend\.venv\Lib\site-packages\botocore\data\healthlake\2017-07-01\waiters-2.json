{"version": 2, "waiters": {"FHIRDatastoreActive": {"operation": "DescribeFHIRDatastore", "delay": 60, "maxAttempts": 360, "acceptors": [{"state": "success", "matcher": "path", "argument": "DatastoreProperties.DatastoreStatus", "expected": "ACTIVE"}, {"state": "failure", "matcher": "path", "argument": "DatastoreProperties.DatastoreStatus", "expected": "CREATE_FAILED"}, {"state": "failure", "matcher": "path", "argument": "DatastoreProperties.DatastoreStatus", "expected": "DELETED"}]}, "FHIRDatastoreDeleted": {"operation": "DescribeFHIRDatastore", "delay": 120, "maxAttempts": 360, "acceptors": [{"state": "success", "matcher": "path", "argument": "DatastoreProperties.DatastoreStatus", "expected": "DELETED"}]}, "FHIRExportJobCompleted": {"operation": "DescribeFHIRExportJob", "delay": 120, "maxAttempts": 360, "acceptors": [{"state": "success", "matcher": "path", "argument": "ExportJobProperties.JobStatus", "expected": "COMPLETED"}, {"state": "success", "matcher": "path", "argument": "ExportJobProperties.JobStatus", "expected": "COMPLETED_WITH_ERRORS"}, {"state": "failure", "matcher": "path", "argument": "ExportJobProperties.JobStatus", "expected": "CANCEL_COMPLETED"}, {"state": "failure", "matcher": "path", "argument": "ExportJobProperties.JobStatus", "expected": "FAILED"}, {"state": "failure", "matcher": "path", "argument": "ExportJobProperties.JobStatus", "expected": "CANCEL_FAILED"}]}, "FHIRImportJobCompleted": {"operation": "DescribeFHIRImportJob", "delay": 120, "maxAttempts": 720, "acceptors": [{"state": "success", "matcher": "path", "argument": "ImportJobProperties.JobStatus", "expected": "COMPLETED"}, {"state": "success", "matcher": "path", "argument": "ImportJobProperties.JobStatus", "expected": "COMPLETED_WITH_ERRORS"}, {"state": "failure", "matcher": "path", "argument": "ImportJobProperties.JobStatus", "expected": "FAILED"}]}}}