import React, { useState } from 'react';
import axios from 'axios';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card } from './ui/card';
import { Label } from './ui/label';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from './ui/tabs';
import { toast } from 'sonner';
import { UserPlus, LogIn, Mail, Lock, User, Calendar } from 'lucide-react';

const Login = ({ onLogin }) => {
  const [loginData, setLoginData] = useState({ email: '', password: '' });
  const [signupData, setSignupData] = useState({ 
    name: '', 
    email: '', 
    password: '', 
    age_group: '' 
  });
  const [loading, setLoading] = useState(false);

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const response = await axios.post('/auth/login', loginData);
      toast.success('Login successful!');
      onLogin(response.data.user, response.data.access_token);
    } catch (error) {
      toast.error(error.response?.data?.detail || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  const handleSignup = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const response = await axios.post('/auth/signup', signupData);
      toast.success('Account created successfully!');
      onLogin(response.data.user, response.data.access_token);
    } catch (error) {
      toast.error(error.response?.data?.detail || 'Signup failed');
    } finally {
      setLoading(false);
    }
  };

  const handleDemoLogin = async () => {
    setLoading(true);
    try {
      // Try to login with demo user, if not exists create one
      const demoCredentials = { email: '<EMAIL>', password: 'demo123' };
      
      try {
        const response = await axios.post('/auth/login', demoCredentials);
        toast.success('Demo login successful!');
        onLogin(response.data.user, response.data.access_token);
      } catch (loginError) {
        // If demo user doesn't exist, create it
        const demoSignup = {
          name: 'Demo User',
          email: '<EMAIL>',
          password: 'demo123',
          age_group: '25-34'
        };
        
        const response = await axios.post('/auth/signup', demoSignup);
        toast.success('Demo account created and logged in!');
        onLogin(response.data.user, response.data.access_token);
      }
    } catch (error) {
      toast.error('Demo login failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8 fade-in">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full mb-4">
            <UserPlus className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome to <span className="text-gradient">RecoSys</span>
          </h1>
          <p className="text-gray-600">
            Your personalized recommendation system
          </p>
        </div>

        {/* Demo Login Button */}
        <Card className="card mb-6 p-6 text-center">
          <h3 className="font-semibold text-gray-900 mb-2">Try Demo Account</h3>
          <p className="text-sm text-gray-600 mb-4">
            Experience the system with pre-loaded data
          </p>
          <Button 
            onClick={handleDemoLogin} 
            disabled={loading}
            className="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-medium"
          >
            {loading ? 'Logging in...' : 'Try Demo'}
          </Button>
        </Card>

        {/* Login/Signup Tabs */}
        <Card className="card p-6 slide-up">
          <Tabs defaultValue="login" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="login" className="flex items-center gap-2">
                <LogIn className="w-4 h-4" />
                Login
              </TabsTrigger>
              <TabsTrigger value="signup" className="flex items-center gap-2">
                <UserPlus className="w-4 h-4" />
                Sign Up
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="login">
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="login-email" className="flex items-center gap-2 text-sm font-medium">
                    <Mail className="w-4 h-4" />
                    Email
                  </Label>
                  <Input
                    id="login-email"
                    type="email"
                    placeholder="Enter your email"
                    value={loginData.email}
                    onChange={(e) => setLoginData({...loginData, email: e.target.value})}
                    required
                    className="bg-white/50 backdrop-blur-sm border-white/20"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="login-password" className="flex items-center gap-2 text-sm font-medium">
                    <Lock className="w-4 h-4" />
                    Password
                  </Label>
                  <Input
                    id="login-password"
                    type="password"
                    placeholder="Enter your password"
                    value={loginData.password}
                    onChange={(e) => setLoginData({...loginData, password: e.target.value})}
                    required
                    className="bg-white/50 backdrop-blur-sm border-white/20"
                  />
                </div>
                
                <Button 
                  type="submit" 
                  disabled={loading}
                  className="w-full btn-primary"
                >
                  {loading ? 'Logging in...' : 'Login'}
                </Button>
              </form>
            </TabsContent>
            
            <TabsContent value="signup">
              <form onSubmit={handleSignup} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="signup-name" className="flex items-center gap-2 text-sm font-medium">
                    <User className="w-4 h-4" />
                    Full Name
                  </Label>
                  <Input
                    id="signup-name"
                    type="text"
                    placeholder="Enter your full name"
                    value={signupData.name}
                    onChange={(e) => setSignupData({...signupData, name: e.target.value})}
                    required
                    className="bg-white/50 backdrop-blur-sm border-white/20"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="signup-email" className="flex items-center gap-2 text-sm font-medium">
                    <Mail className="w-4 h-4" />
                    Email
                  </Label>
                  <Input
                    id="signup-email"
                    type="email"
                    placeholder="Enter your email"
                    value={signupData.email}
                    onChange={(e) => setSignupData({...signupData, email: e.target.value})}
                    required
                    className="bg-white/50 backdrop-blur-sm border-white/20"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="signup-password" className="flex items-center gap-2 text-sm font-medium">
                    <Lock className="w-4 h-4" />
                    Password
                  </Label>
                  <Input
                    id="signup-password"
                    type="password"
                    placeholder="Create a password"
                    value={signupData.password}
                    onChange={(e) => setSignupData({...signupData, password: e.target.value})}
                    required
                    className="bg-white/50 backdrop-blur-sm border-white/20"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="age-group" className="flex items-center gap-2 text-sm font-medium">
                    <Calendar className="w-4 h-4" />
                    Age Group
                  </Label>
                  <select
                    id="age-group"
                    value={signupData.age_group}
                    onChange={(e) => setSignupData({...signupData, age_group: e.target.value})}
                    className="custom-select w-full"
                  >
                    <option value="">Select age group</option>
                    <option value="18-24">18-24</option>
                    <option value="25-34">25-34</option>
                    <option value="35-44">35-44</option>
                    <option value="45-54">45-54</option>
                    <option value="55+">55+</option>
                  </select>
                </div>
                
                <Button 
                  type="submit" 
                  disabled={loading}
                  className="w-full btn-primary"
                >
                  {loading ? 'Creating account...' : 'Sign Up'}
                </Button>
              </form>
            </TabsContent>
          </Tabs>
        </Card>
        
        <div className="text-center mt-6 text-sm text-gray-500">
          Built with FastAPI, React & Machine Learning
        </div>
      </div>
    </div>
  );
};

export default Login;