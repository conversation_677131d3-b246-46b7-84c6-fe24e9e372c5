{"pagination": {"ListCredentialLockers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Items"}, "ListDestinations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "DestinationList"}, "ListEventLogConfigurations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "EventLogConfigurationList"}, "ListManagedThingSchemas": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Items"}, "ListManagedThings": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Items"}, "ListNotificationConfigurations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "NotificationConfigurationList"}, "ListOtaTaskConfigurations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Items"}, "ListOtaTaskExecutions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ExecutionSummaries"}, "ListOtaTasks": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Tasks"}, "ListProvisioningProfiles": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Items"}, "ListSchemaVersions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Items"}, "ListAccountAssociations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Items"}, "ListCloudConnectors": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Items"}, "ListConnectorDestinations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ConnectorDestinationList"}, "ListDeviceDiscoveries": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Items"}, "ListDiscoveredDevices": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Items"}, "ListManagedThingAccountAssociations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Items"}}}