name: Deploy Frontend to Netlify

on:
  push:
    branches: [ main, master ]
    paths:
      - 'Products-Recommendation-System/frontend/**'
      - '.github/workflows/deploy-frontend-netlify.yml'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: Products-Recommendation-System/frontend
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Use Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'
          cache-dependency-path: Products-Recommendation-System/frontend/yarn.lock

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Build
        env:
          REACT_APP_BACKEND_URL: ${{ secrets.REACT_APP_BACKEND_URL }}
          WDS_SOCKET_PORT: 443
        run: |
          yarn build

      - name: Deploy to Netlify
        uses: netlify/actions/cli@v4
        with:
          args: deploy --dir=build --prod --message "GitHub Actions deploy"
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}