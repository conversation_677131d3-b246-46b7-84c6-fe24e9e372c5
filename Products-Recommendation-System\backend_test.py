import requests
import sys
import json
from datetime import datetime

class MLRecommendationSystemTester:
    def __init__(self, base_url="https://recsys-craft.preview.emergentagent.com/api"):
        self.base_url = base_url
        self.token = None
        self.user_id = None
        self.tests_run = 0
        self.tests_passed = 0
        self.demo_user = {"email": "<EMAIL>", "password": "demo123"}

    def run_test(self, name, method, endpoint, expected_status, data=None, headers=None):
        """Run a single API test"""
        url = f"{self.base_url}/{endpoint}"
        test_headers = {'Content-Type': 'application/json'}
        
        if self.token:
            test_headers['Authorization'] = f'Bearer {self.token}'
        
        if headers:
            test_headers.update(headers)

        self.tests_run += 1
        print(f"\n🔍 Testing {name}...")
        
        try:
            if method == 'GET':
                response = requests.get(url, headers=test_headers)
            elif method == 'POST':
                response = requests.post(url, json=data, headers=test_headers)
            elif method == 'PUT':
                response = requests.put(url, json=data, headers=test_headers)
            elif method == 'DELETE':
                response = requests.delete(url, headers=test_headers)

            success = response.status_code == expected_status
            if success:
                self.tests_passed += 1
                print(f"✅ Passed - Status: {response.status_code}")
                try:
                    return success, response.json()
                except:
                    return success, {}
            else:
                print(f"❌ Failed - Expected {expected_status}, got {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"   Error: {error_detail}")
                except:
                    print(f"   Error: {response.text}")
                return False, {}

        except Exception as e:
            print(f"❌ Failed - Error: {str(e)}")
            return False, {}

    def test_health_check(self):
        """Test health endpoint"""
        success, response = self.run_test(
            "Health Check",
            "GET",
            "health",
            200
        )
        return success

    def test_demo_login(self):
        """Test demo user login"""
        success, response = self.run_test(
            "Demo User Login",
            "POST",
            "auth/login",
            200,
            data=self.demo_user
        )
        if success and 'access_token' in response:
            self.token = response['access_token']
            self.user_id = response['user']['user_id']
            print(f"   Logged in as: {response['user']['name']} (ID: {self.user_id})")
            return True
        return False

    def test_signup_new_user(self):
        """Test creating a new user"""
        test_user = {
            "name": f"Test User {datetime.now().strftime('%H%M%S')}",
            "email": f"test_{datetime.now().strftime('%H%M%S')}@example.com",
            "password": "TestPass123!",
            "age_group": "25-34"
        }
        
        success, response = self.run_test(
            "User Signup",
            "POST",
            "auth/signup",
            200,
            data=test_user
        )
        return success

    def test_get_products(self):
        """Test getting products"""
        success, response = self.run_test(
            "Get Products",
            "GET",
            "products?limit=10",
            200
        )
        if success and isinstance(response, list) and len(response) > 0:
            print(f"   Found {len(response)} products")
            return True
        return False

    def test_get_categories(self):
        """Test getting categories"""
        success, response = self.run_test(
            "Get Categories",
            "GET",
            "categories",
            200
        )
        if success and 'categories' in response and len(response['categories']) > 0:
            print(f"   Found categories: {response['categories'][:5]}...")
            return True
        return False

    def test_get_product_by_category(self):
        """Test filtering products by category"""
        success, response = self.run_test(
            "Get Products by Category",
            "GET",
            "products?category=Electronics&limit=5",
            200
        )
        if success and isinstance(response, list):
            print(f"   Found {len(response)} Electronics products")
            return True
        return False

    def test_item_based_recommendations(self):
        """Test item-based collaborative filtering recommendations"""
        success, response = self.run_test(
            "Item-Based Recommendations",
            "GET",
            "recommendations?method=item_based&n=5",
            200
        )
        if success and isinstance(response, list):
            print(f"   Got {len(response)} item-based recommendations")
            if len(response) > 0:
                print(f"   Sample: {response[0]['title']} (Score: {response[0]['score']:.2f})")
            return True
        return False

    def test_svd_recommendations(self):
        """Test SVD matrix factorization recommendations"""
        success, response = self.run_test(
            "SVD Recommendations",
            "GET",
            "recommendations?method=svd&n=5",
            200
        )
        if success and isinstance(response, list):
            print(f"   Got {len(response)} SVD recommendations")
            if len(response) > 0:
                print(f"   Sample: {response[0]['title']} (Score: {response[0]['score']:.2f})")
            return True
        return False

    def test_create_interaction(self):
        """Test creating user interactions"""
        # First get a product to interact with
        success, products = self.run_test(
            "Get Product for Interaction",
            "GET",
            "products?limit=1",
            200
        )
        
        if not success or not products:
            return False
            
        product_id = products[0]['product_id']
        
        # Test different interaction types
        interaction_types = [
            {"event_type": "view", "value": 1},
            {"event_type": "click", "value": 2},
            {"event_type": "add_to_cart", "value": 4}
        ]
        
        all_success = True
        for interaction in interaction_types:
            interaction_data = {
                "product_id": product_id,
                "event_type": interaction["event_type"],
                "value": interaction["value"]
            }
            
            success, response = self.run_test(
                f"Create {interaction['event_type']} Interaction",
                "POST",
                "interactions",
                200,
                data=interaction_data
            )
            if not success:
                all_success = False
        
        return all_success

    def test_user_history(self):
        """Test getting user interaction history"""
        success, response = self.run_test(
            "Get User History",
            "GET",
            "user/history",
            200
        )
        if success and isinstance(response, list):
            print(f"   Found {len(response)} interactions in history")
            return True
        return False

    def test_analytics_data(self):
        """Test analytics endpoint"""
        success, response = self.run_test(
            "Get Analytics Data",
            "GET",
            "analytics",
            200
        )
        if success and isinstance(response, dict):
            required_fields = ['category_distribution', 'popular_products', 'total_users', 'total_products', 'total_interactions']
            has_all_fields = all(field in response for field in required_fields)
            if has_all_fields:
                print(f"   Users: {response['total_users']}, Products: {response['total_products']}, Interactions: {response['total_interactions']}")
                print(f"   Categories: {len(response['category_distribution'])}, Popular Products: {len(response['popular_products'])}")
                return True
        return False

    def test_recommendation_personalization(self):
        """Test that recommendations are personalized"""
        # Get initial recommendations
        success1, recs1 = self.run_test(
            "Initial Recommendations",
            "GET",
            "recommendations?method=item_based&n=5",
            200
        )
        
        if not success1:
            return False
            
        # Create some interactions
        success, products = self.run_test(
            "Get Products for Personalization Test",
            "GET",
            "products?limit=3",
            200
        )
        
        if success and products:
            for product in products[:2]:
                self.run_test(
                    f"Interaction for Personalization",
                    "POST",
                    "interactions",
                    200,
                    data={
                        "product_id": product['product_id'],
                        "event_type": "add_to_cart",
                        "value": 4
                    }
                )
        
        # Get new recommendations
        success2, recs2 = self.run_test(
            "Updated Recommendations",
            "GET",
            "recommendations?method=item_based&n=5",
            200
        )
        
        if success2:
            print(f"   Recommendations updated after interactions")
            return True
        return False

    def test_ml_system_quality(self):
        """Test ML system quality metrics"""
        print("\n🧠 Testing ML System Quality...")
        
        # Test both recommendation methods
        methods = ['item_based', 'svd']
        method_results = {}
        
        for method in methods:
            success, response = self.run_test(
                f"{method.upper()} Recommendations Quality",
                "GET",
                f"recommendations?method={method}&n=10",
                200
            )
            
            if success and response:
                scores = [rec['score'] for rec in response]
                method_results[method] = {
                    'count': len(response),
                    'avg_score': sum(scores) / len(scores) if scores else 0,
                    'max_score': max(scores) if scores else 0,
                    'min_score': min(scores) if scores else 0
                }
                print(f"   {method}: {len(response)} recs, avg score: {method_results[method]['avg_score']:.3f}")
        
        return len(method_results) == 2

def main():
    print("🚀 Starting ML Recommendation System API Tests")
    print("=" * 60)
    
    tester = MLRecommendationSystemTester()
    
    # Basic connectivity tests
    if not tester.test_health_check():
        print("❌ Health check failed, stopping tests")
        return 1
    
    # Authentication tests
    if not tester.test_demo_login():
        print("❌ Demo login failed, stopping tests")
        return 1
    
    # Test signup (optional)
    tester.test_signup_new_user()
    
    # Product catalog tests
    if not tester.test_get_products():
        print("❌ Product retrieval failed")
        return 1
    
    if not tester.test_get_categories():
        print("❌ Categories retrieval failed")
        return 1
    
    tester.test_get_product_by_category()
    
    # ML Recommendation tests
    if not tester.test_item_based_recommendations():
        print("❌ Item-based recommendations failed")
        return 1
    
    if not tester.test_svd_recommendations():
        print("❌ SVD recommendations failed")
        return 1
    
    # Interaction tests
    if not tester.test_create_interaction():
        print("❌ Interaction creation failed")
        return 1
    
    tester.test_user_history()
    
    # Analytics tests
    if not tester.test_analytics_data():
        print("❌ Analytics data failed")
        return 1
    
    # Advanced ML tests
    tester.test_recommendation_personalization()
    tester.test_ml_system_quality()
    
    # Print final results
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {tester.tests_passed}/{tester.tests_run} tests passed")
    
    if tester.tests_passed == tester.tests_run:
        print("🎉 All tests passed! ML Recommendation System is working correctly.")
        return 0
    else:
        failed_tests = tester.tests_run - tester.tests_passed
        print(f"⚠️  {failed_tests} test(s) failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())