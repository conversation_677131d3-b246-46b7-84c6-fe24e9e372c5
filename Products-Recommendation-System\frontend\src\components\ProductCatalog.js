import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { toast } from 'sonner';
import { 
  Search, 
  Filter, 
  ShoppingCart, 
  Eye, 
  Star,
  Package,
  Grid,
  List
} from 'lucide-react';

const ProductCatalog = ({ user }) => {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [viewMode, setViewMode] = useState('grid');

  // Return an image URL for a product; use backend-provided image_url if available,
  // otherwise generate a deterministic placeholder based on product_id
  const getProductImage = (product, w = 400, h = 300) => {
    if (product.image_url) return product.image_url;
    return `https://picsum.photos/seed/${encodeURIComponent(product.product_id)}/${w}/${h}`;
  };

  useEffect(() => {
    loadProducts();
    loadCategories();
  }, [selectedCategory]);

  const loadProducts = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (selectedCategory) params.append('category', selectedCategory);
      params.append('limit', '100');
      
      const response = await axios.get(`/products?${params}`);
      setProducts(response.data);
    } catch (error) {
      toast.error('Failed to load products');
      console.error('Products error:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const response = await axios.get('/categories');
      setCategories(response.data.categories);
    } catch (error) {
      console.error('Categories error:', error);
    }
  };

  const handleProductInteraction = async (productId, eventType) => {
    try {
      await axios.post('/interactions', {
        product_id: productId,
        event_type: eventType,
        value: eventType === 'view' ? 1 : eventType === 'click' ? 2 : 4
      });
      
      toast.success(`${eventType} recorded!`);
    } catch (error) {
      toast.error('Failed to record interaction');
    }
  };

  const filteredProducts = products.filter(product =>
    product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.brand.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const ProductCard = ({ product }) => (
    <div className="card-compact group hover:scale-105 transition-all duration-300">
      <div className="w-full h-48 rounded-lg mb-4 overflow-hidden bg-gray-100">
        <img
          src={getProductImage(product, 600, 400)}
          alt={product.title}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          onError={(e) => { e.currentTarget.src = getProductImage(product, 600, 400); }}
        />
      </div>
      
      <div className="space-y-3">
        <div>
          <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
            {product.title}
          </h3>
          <p className="text-sm text-gray-600">{product.category} • {product.brand}</p>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-lg font-bold text-green-600">₹{product.price}</span>
          <div className="flex items-center gap-1 text-sm text-yellow-600">
            <Star className="w-4 h-4 fill-current" />
            <span>{(Math.random() * 2 + 3).toFixed(1)}</span>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleProductInteraction(product.product_id, 'view')}
            className="flex-1 flex items-center justify-center gap-1"
          >
            <Eye className="w-4 h-4" />
            View
          </Button>
          <Button
            size="sm"
            onClick={() => handleProductInteraction(product.product_id, 'add_to_cart')}
            className="flex-1 flex items-center justify-center gap-1 btn-primary"
          >
            <ShoppingCart className="w-4 h-4" />
            Add
          </Button>
        </div>
      </div>
    </div>
  );

  const ProductListItem = ({ product }) => (
    <div className="card-compact flex items-center gap-4 hover:bg-blue-50 transition-colors">
      <div className="w-20 h-20 rounded-lg overflow-hidden bg-gray-100">
        <img
          src={getProductImage(product, 160, 160)}
          alt={product.title}
          className="w-full h-full object-cover"
          onError={(e) => { e.currentTarget.src = getProductImage(product, 160, 160); }}
        />
      </div>
      
      <div className="flex-1 min-w-0">
        <h3 className="font-semibold text-gray-900">{product.title}</h3>
        <p className="text-sm text-gray-600">{product.category} • {product.brand}</p>
        <p className="text-xs text-gray-500 mt-1">{product.description}</p>
      </div>
      
      <div className="text-right">
        <div className="text-lg font-bold text-green-600 mb-2">₹{product.price}</div>
        <div className="flex items-center gap-1 text-sm text-yellow-600 mb-3">
          <Star className="w-4 h-4 fill-current" />
          <span>{(Math.random() * 2 + 3).toFixed(1)}</span>
        </div>
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleProductInteraction(product.product_id, 'view')}
          >
            <Eye className="w-4 h-4" />
          </Button>
          <Button
            size="sm"
            onClick={() => handleProductInteraction(product.product_id, 'add_to_cart')}
            className="btn-primary"
          >
            <ShoppingCart className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="loading-dots">
              <div></div>
              <div></div>
              <div></div>
              <div></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="fade-in">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Product <span className="text-gradient">Catalog</span>
          </h1>
          <p className="text-gray-600">
            Discover and interact with our product collection
          </p>
        </div>

        {/* Search and Filters */}
        <div className="card p-6 slide-up">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="flex-1 w-full sm:max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  type="text"
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-white/50 backdrop-blur-sm border-white/20"
                />
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Filter className="w-4 h-4 text-gray-600" />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="custom-select"
                >
                  <option value="">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
              
              <div className="flex items-center gap-1 bg-white/50 backdrop-blur-sm border border-white/20 rounded-lg p-1">
                <Button
                  size="sm"
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  onClick={() => setViewMode('grid')}
                  className="p-2"
                >
                  <Grid className="w-4 h-4" />
                </Button>
                <Button
                  size="sm"
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  onClick={() => setViewMode('list')}
                  className="p-2"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Products */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              {filteredProducts.length} Products Found
            </h2>
            {selectedCategory && (
              <div className="text-sm text-gray-600">
                Category: <span className="font-medium">{selectedCategory}</span>
              </div>
            )}
          </div>
          
          {filteredProducts.length > 0 ? (
            <div className={
              viewMode === 'grid' 
                ? 'product-grid' 
                : 'space-y-4'
            }>
              {filteredProducts.map((product, index) => (
                <div 
                  key={product.product_id}
                  className="bounce-in"
                  style={{ animationDelay: `${(index % 12) * 0.05}s` }}
                >
                  {viewMode === 'grid' ? (
                    <ProductCard product={product} />
                  ) : (
                    <ProductListItem product={product} />
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 text-gray-500">
              <Package className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p>No products found matching your criteria</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductCatalog;